# These are some examples of commonly ignored file patterns.
# You should customize this list as applicable to your project.
# Learn more about .gitignore:
#     https://www.atlassian.com/git/tutorials/saving-changes/gitignore

# Environment files
.env.development

# Node artifact files
src/__version.ts
node_modules/
dist/

.install-guide

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# JetBrains IDE
.idea/

# VS Code settings
.vscode/

app/

debug-audio/

# Unit test reports
TEST*.xml

# Generated by MacOS
.DS_Store

# Generated by Windows
Thumbs.db

# Applications
*.app
*.exe
*.war

# Large media files
*.mp4
*.tiff
*.avi
*.flv
*.mov
*.wmv
