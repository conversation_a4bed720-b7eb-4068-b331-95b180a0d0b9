## State Machine Concurrency Control

This project uses a mutex-based transition control system for its event-driven state machine, ensuring robust, race-free, and prioritized state transitions. The system supports urgent transitions (like barge-in/interruption) that can preempt normal transitions, and guarantees serialization for all others.

For details, see [`docs/mutex.md`](docs/mutex.md).
# AudioConnector Server Reference Guide

### Purpose

This repository contains a sample implementation for an AudioConnector Server. This is to be used as a guide to help understand some of the basics of setting up an AudioConnector Server. It is not intended for production purposes. Protocol documentation can be found on the [Genesys Developer Portal](https://developer.genesys.cloud/devapps/audiohook/).

### Speech Service Support

The implementation now includes full support for both Azure Speech Services and Google Cloud Speech Services:

- Real-time speech recognition (ASR)
- High-quality text-to-speech synthesis (TTS)
- Configurable through environment variables
- Easy switching between providers
- Support for both Azure and Google Cloud credentials

For detailed configuration instructions, see [Speech Service Configuration](./docs/speech-service-configuration.md).

### RequestId Tracking and Metrics Association

Every user input event (speech, DTMF, etc.) is assigned a unique `requestId` at the start of processing. This `requestId` is required in all downstream events and metrics, ensuring robust, end-to-end tracking and observability for every conversational turn.

- The `requestId` is generated in the state machine's `StartMetricsRequestAction` and enforced by the metrics system.
- If a metrics phase is started without a valid `requestId`, an error is thrown.
- See [src/services/monitoring/request-id-lifecycle.md](src/services/monitoring/request-id-lifecycle.md) and [src/session/state-machine/README.md](src/session/state-machine/README.md) for details.
## Application Flow and State Machine

This application implements a voice-based conversational AI system with the following core flow:

1. **Customer speaks** → ASR (Speech-to-Text) converts speech to text
2. **Text is sent** → LLM (Bot Service) processes and generates a response
3. **Response is converted** → TTS (Text-to-Speech) converts text to audio
4. **Audio is played** to the customer

### State Machine Diagram

````mermaid
flowchart LR
    Start([Start]) --> Connecting
    Connecting[Connecting] -->|WebSocket Connection| Authenticating[Authenticating]
    Authenticating -->|Authentication Successful| Configuring[Configuring]
    Configuring -->|Open Message Sent| Connected[Connected]
    Connected -->|Opened Message Received| Listening[Listening]

    subgraph Conversation
        Listening -->|Audio Received| Processing[Processing]
        Processing -->|ASR Complete| Responding[Responding]
        Responding -->|LLM Response Generated| Speaking[Speaking]
        Speaking -->|TTS Playback Complete| Listening

        %% Barge-in and DTMF barge-in now supported via BargeInManager
      end

      Listening -->|DTMF Digit Received| DTMFCapture[DTMF Capture]
      DTMFCapture -->|DTMF Sequence Complete| Responding

      Conversation -->|Session End or Error| Disconnecting[Disconnecting]
      Disconnecting -->|Connection Closed| End([End])
    ```

### Barge-In and Playback State Management

#### Centralized Barge-In and Playback State

Barge-in (both audio and DTMF) and playback state are now managed centrally by the `BargeInManager` class. All components (Session, AudioManager, DTMFManager) interact with playback state and barge-in events through this manager, ensuring robust and maintainable handling of interruptions and state transitions.

- **BargeInManager**: Central authority for playback state and barge-in event routing.
- **Session**: Orchestrates the conversation and delegates barge-in/playback state to BargeInManager.
- **AudioManager/DTMFManager**: Consume playback state and signal barge-in events via BargeInManager.

See [docs/barge-in-simplification.md](./docs/barge-in-simplification.md) for architectural details and implementation progress.

### Component Interaction Flow

```mermaid
graph LR
    %% Define participants
    Client[Client]
    Session[Session]
    ASR[ASR Service]
    Bot[Bot Service]
    TTS[TTS Service]

    %% Connection setup
    Client -->|1. WebSocket Connection| Session
    Session -->|2. Connection Accepted| Client
    Client -->|3. Open Message| Session
    Session -->|4. Opened Message| Client

    %% Main conversation flow
    Client -->|5. Binary Audio Data| Session
    Session -->|6. Process Audio| ASR
    ASR -->|7. Final Transcript| Session
    Session -->|8. Get Response| Bot
    Bot -->|9. Text Response| Session
    Session -->|10. Bot Turn Response Event| Client
    Session -->|11. Convert to Speech| TTS
    TTS -->|12. Audio Bytes| Session
    Session -->|13. Binary Audio Data| Client
    Client -->|14. Playback Started| Session
    Client -->|15. Playback Completed| Session

    %% DTMF flow
    Client -->|16. DTMF Message| Session
    Session -->|17. Get Response for DTMF| Bot

    %% Session end
    Bot -->|18. End Session Flag| Session
    Session -->|19. Disconnect Message| Client
    Client -->|20. Close Connection| Session

    %% Current limitations
    style LimitBox fill:#f9f,stroke:#333,stroke-width:2px
    subgraph LimitBox[Current Limitations]
        L1[No barge-in support]
        L2[Input ignored during playback]
        L3[DTMF ignored during playback]
    end

    %% Notes for clarity
    style NoteBox fill:#ffd,stroke:#333,stroke-width:1px
    subgraph NoteBox[Key Notes]
        N1[Session orchestrates all interactions]
        N2[Same WebSocket connection used for all messages]
        N3[Audio flows in both directions as binary data]
    end
````

### Key Components and Their Roles

#### The main session object

The [Session](./src/common/session.ts) class contains methods and logic that handle communicating with the AudioConnector Client. It orchestrates the entire conversation flow and maintains the state of the interaction.

#### Speech Services

The [Speech Service](./src/services/speech/) implementation provides ASR and TTS capabilities through either Azure Speech Services or Google Cloud Speech Services. The service provider can be configured through environment variables.

- **ASR Service**: Converts audio to text and emits transcript events
- **TTS Service**: Converts text responses to audio for playback

#### Bot Service

The [BotService](./src/services/bot-service.ts) class is responsible for getting the metadata for a specified Bot, as well as interacting with the Bot itself. It processes user input and generates appropriate responses using LLM technology.

#### DTMF Service

The [DTMFService](./src/services/dtmf-service.ts) class is responsible for interpreting any DTMF digits received from the AudioConnector Client. It collects digits until a terminating character is received and then processes the complete sequence.

#### Secret Service

The [SecretService](./src/services/secret-service.ts) class is responsible for looking up the secret from a given API Key used during the initial authentication process.

### State Transitions

1. **Connection Establishment**:

   - WebSocket connection is established
   - Authentication is performed
   - Session is configured with initial parameters

2. **Conversation Loop**:

   - Audio is received and processed by ASR (only when not playing audio)
   - Transcripts are sent to the Bot Service
   - Bot responses are converted to speech by TTS
   - Audio is sent back to the client
   - During audio playback, all user input is ignored

3. **DTMF Handling**:

   - DTMF digits are collected and processed (only when not playing audio)
   - Complete sequences trigger Bot responses
   - DTMF input during audio playback is ignored

4. **Session Termination**:
   - Bot can signal session end
   - Error conditions can trigger disconnection
   - Client can close the connection

### Running the server

#### Requirements

This implementation was written using NodeJS 18.16.0 as a target. If you are using a Node version manager, there is a [nvmrc](./.nvmrc) file that specifies this version.

#### Steps to run the server locally

1. Run `npm install` in the root of the project.
2. Configure your speech service provider in the `.env` file (see [configuration guide](./docs/speech-service-configuration.md)).
3. Run `npm run start` in the root of the project to start the server. The port can be adjusted from within the [environment](./.env) file.

### Architecture

The speech service implementation uses modern software patterns:

- Abstract Factory pattern for service creation
- Dependency Injection for service selection
- Strategy pattern for interchangeable speech providers
- Event-based communication for ASR results

For implementation details, see [Google Cloud Integration Plan](./docs/google-cloud-integration-plan.md).

---

## Metrics Finalization Fix (May 2025)

### What Changed

A minimal fix was implemented to ensure that metrics for each user interaction (including `llmProcessing` and `textToSpeech` phases) are finalized and recorded immediately after every bot response, not just at session end. This improves the accuracy and granularity of per-interaction metrics.

**Implementation:**
A call to `metricsAdapter.finalizeRequest()` was added at the end of the bot response handling action in the state machine:

```ts
// src/session/state-machine/actions/processing-bot/handle-bot-response-action.ts
const metricsAdapter = session.getMetricsAdapter?.();
if (metricsAdapter && typeof metricsAdapter.finalizeRequest === 'function') {
  await metricsAdapter.finalizeRequest();
}
```

### How to Test / Verify

- Run a conversation session and interact with the bot.
- After each user input and bot response, verify that metrics for the interaction are finalized (e.g., check logs, metrics output, or test hooks).
- Ensure that metrics are not only finalized at session end, but after every user-bot exchange.

#### Example Test Steps

- Start a session and send multiple user inputs.
- Confirm that metrics for each turn (llmProcessing, textToSpeech) are finalized after each bot response.
- Review logs or metrics output for per-interaction finalization.

#### Success Criteria

- Metrics for each user interaction are finalized and available immediately after each bot response.
- No unrelated state machine or session logic is affected.

---
