apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Release.Name }}-connector
  labels:
    app.kubernetes.io/name: {{ .Release.Name }}-connector
spec:
  selector:
    matchLabels:
      app: connector
  template:
    metadata:
      labels:
        app: connector
    spec:
      terminationGracePeriodSeconds: 60
      imagePullSecrets:
        - name: {{ .Release.Name }}-gitlab-dockerconfig
      containers:
        - image: network.git.cz.o2:5005/deployments/aidcc-ccczautomationllmconnector:1.4.3
          imagePullPolicy: Always
          name: connector
          ports:
            - containerPort: 8080
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          readinessProbe:
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 3
            periodSeconds: 10
          livenessProbe:
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 3
            periodSeconds: 10
          env:
            - name: NODE_ENV
              value: {{ .Values.connector.NODE_ENV | default "production" }}
            - name: PORT
              value: "{{ .Values.connector.PORT | default "8080" }}"
            - name: APPLICATION_NAME
              value: {{ .Values.connector.APPLICATION_NAME | default "CCCZAutomationLLMConnector" }}
          envFrom:
            - configMapRef:
                name: {{ .Release.Name }}-env
