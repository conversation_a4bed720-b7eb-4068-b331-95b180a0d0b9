# Declare variables to be passed into your templates.

# -------------------------------------
# Prometheus Alerting Thresholds
# -------------------------------------
alerting:
  enabled: false

# -------------------------------------
# Connector Configuration
# -------------------------------------
connector:
  PORT: 8080
  APPLICATION_NAME: CCCZAutomationLLMConnector
  NODE_ENV: development
  REDIS_PASSWORD: redis123
  REDIS_PORT: 6379

# -------------------------------------
# Environment Variables
# -------------------------------------
environment:
  # Speech-to-Text (STT) Configuration
  SPEECH_SERVICE: 'google'
  # Google Cloud STT Configuration
  GOOGLE_PROJECT_ID: 'o2-glc-test'
  GOOGLE_APPLICATION_CREDENTIALS: 'google-credentials.json'
  # Azure STT Configuration
  AZURE_SPEECH_KEY: '********************************'
  AZURE_SPEECH_REGION: 'westeurope'

  # Text-to-Speech (TTS) Configuration
  TTS_PROVIDER: 'elevenlabs'
  # ElevenLabs TTS Configuration
  ELEVENLABS_API_KEY: '***************************************************'
  ELEVENLABS_VOICE_ID: 'SZXidiHhq5QYe3jRboSZ'

  # Language Model (LLM) Configuration
  LLM_PROVIDER_TYPE: 'litellm'
  # LiteLLM Configuration
  LITELLM_API_KEY: 'sk-m-gMUCL9sbrdmri5Sr-Heg'
  LITELLM_ENDPOINT: 'https://litellm.ai-sandbox.azure.to2cz.cz'
  LITELLM_MODEL: 'gpt-4o-mini'

  # Server Configuration
  PORT: '8080'
  IS_DEBUG_MODE: 'TRUE'
  DEBUG_AUDIO: 'TRUE'

  # Monitoring & Performance
  ENABLE_PERF_MONITOR: 'true'

  # Database Configuration
  MOCKING_DATABASE: 'true'

  # Network Configuration
  HTTP_PROXY: 'http://internet-proxy-b1.cz.o2:8080'
  NO_PROXY: 'localhost,127.0.0.1,litellm.ai-sandbox.azure.to2cz.cz'
  GRPC_DNS_RESOLVER: 'native'

  # Barge-in and Streaming Configuration
  ENABLE_BARGE_IN: 'true'
  BARGE_IN_CONFIDENCE_THRESHOLD: '0.6'
  INTERIM_TRANSCRIPT_STABILITY_THRESHOLD: '0.5'
  ENABLE_STABLE_STREAM: 'true'
  PAUSE_THRESHOLD_MS: '1500'
  ASR_STREAMING_MODE: 'continuous'
  DEBUG_BARGE_IN: 'true'

  # Logging Configuration
  LOG_LEVEL: 'DEBUG'
  METRICS_LOG_LEVEL: '1'
  LOG_HTTP_REQUESTS: 'false'
  LOG_HTTP_RESPONSES: 'false'
  LOG_OPENAI_REQUESTS: 'false'
  LOG_OPENAI_RESPONSES: 'false'
  LOG_WEBSOCKET_MESSAGES: 'false'
  LOG_ASR_INTERIM: 'false'
  LOG_ASR_RAW_RESPONSES: 'false'
  LOG_ASR_PARSED_RESULTS: 'false'
  LOG_ASR_UNSTABLE_TRANSCRIPTS: 'false'
  OPENAI_LOG_FORMAT: 'minimal'

# -------------------------------------
# Service Configuration
# -------------------------------------
service:
  type: ClusterIP
  port: 8080

# -------------------------------------
# Ingress Configuration
# -------------------------------------
ingress:
  enabled: true
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.org/proxy-connect-timeout: 30s
    nginx.org/proxy-read-timeout: 360s
    nginx.org/websocket-services: connector
  ingressClassName: nginx
  domainname: aicoretest.o2.cz
  domainnameinternal: aidcc-ccczautomationllmconnector-dev.aks-cc.network.cz.o2
  hosts:
    - host: aidcc-ccczautomationllmconnector-dev.aks-cc.network.cz.o2
      paths:
        - path: /
          pathType: Prefix
  tls:
    enabled: true

# -------------------------------------
# Resources Configuration
# -------------------------------------
resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 100m
    memory: 128Mi
