# Application Flow: From Session Setup to ASR-LLM-TTS Turns

This document provides a visual representation of the application flow from session setup through the ASR-LLM-TTS conversation turns. It includes sequence diagrams, state machine diagrams, and combined flow diagrams to help understand the system from different perspectives.

## 1. Sequence Diagram

This diagram shows the temporal flow of events and interactions between components during a typical conversation turn:

```mermaid
sequenceDiagram
    participant Client
    participant Session
    participant ASR
    participant Bot
    participant TTS
    participant StateManager

    %% Session Initialization
    Client->>Session: Connect
    Session->>StateManager: Initialize
    StateManager->>StateManager: INITIALIZING → IDLE

    %% ASR Phase
    Client->>Session: Audio data
    Session->>ASR: Process audio
    Note over Session,ASR: Start 'speechToText' phase
    ASR->>Session: Interim transcripts
    ASR->>Session: Final transcript
    Note over Session,ASR: End 'speechToText' phase
    Session->>StateManager: USER_INPUT_PROCESSED
    StateManager->>StateManager: IDLE → PROCESSING_INPUT → PROCESSING_BOT

    %% LLM Phase
    Note over Session,Bot: Start 'llmProcessing' phase
    Session->>Bot: Process user input
    Bot->>Session: Bot response
    Note over Session,Bot: End 'llmProcessing' phase
    Session->>StateManager: BOT_RESPONSE_RECEIVED
    StateManager->>StateManager: PROCESSING_BOT → RESPONDING

    %% TTS Phase
    Note over Session,TTS: Start 'textToSpeech' phase
    Session->>TTS: Convert text to speech
    TTS->>Session: Audio data
    Session->>StateManager: RESPONSE_PREPARATION_COMPLETED
    StateManager->>StateManager: RESPONDING → PLAYING
    Session->>Client: Play audio
    Client->>Session: Playback completed
    Note over Session,TTS: End 'textToSpeech' phase
    Session->>StateManager: PLAYBACK_COMPLETED
    StateManager->>StateManager: PLAYING → IDLE

    %% Barge-In Scenario
    Note over Client,StateManager: Barge-In Scenario
    Client->>Session: Audio data (during playback)
    Session->>ASR: Process audio
    ASR->>Session: Interim transcript
    Session->>StateManager: BARGE_IN_DETECTED
    StateManager->>StateManager: PLAYING → PROCESSING_INPUT
    Session->>Client: Stop playback
```

## 2. State Machine Diagram

This diagram focuses on the state transitions during the ASR-LLM-TTS flow, with annotations for metrics tracking phases:

```mermaid
stateDiagram-v2
    [*] --> IDLE

    %% Normal Flow with Metrics
    IDLE --> PROCESSING_INPUT: USER_INPUT_RECEIVED
    note right of PROCESSING_INPUT: Start 'speechToText' phase
    PROCESSING_INPUT --> PROCESSING_BOT: USER_INPUT_PROCESSED
    note right of PROCESSING_INPUT: End 'speechToText' phase
    note right of PROCESSING_BOT: Start 'llmProcessing' phase
    PROCESSING_BOT --> RESPONDING: BOT_RESPONSE_RECEIVED
    note right of PROCESSING_BOT: End 'llmProcessing' phase
    note right of RESPONDING: Start 'textToSpeech' phase
    RESPONDING --> PLAYING: RESPONSE_PREPARATION_COMPLETED
    PLAYING --> IDLE: PLAYBACK_COMPLETED
    note right of IDLE: End 'textToSpeech' phase

    %% Barge-In Flow
    PLAYING --> PROCESSING_INPUT: BARGE_IN_DETECTED
```

## 3. Combined Flow Diagram

This diagram combines elements of both sequence and state diagrams to show both the component interactions and state transitions in a single view:

```mermaid
flowchart TD
    %% States
    IDLE[IDLE State]
    PROC_IN[PROCESSING_INPUT State]
    PROC_BOT[PROCESSING_BOT State]
    RESP[RESPONDING State]
    PLAY[PLAYING State]

    %% Components
    Client[Client]
    ASR[ASR Service]
    Bot[Bot Service]
    TTS[TTS Service]

    %% Normal Flow
    IDLE -->|USER_INPUT_RECEIVED| PROC_IN
    Client -->|Audio Data| ASR
    ASR -->|Process Audio| PROC_IN

    %% ASR Phase
    subgraph "ASR Phase (speechToText)"
        PROC_IN -->|Transcription| ASR_OUT[Final Transcript]
    end

    ASR_OUT -->|USER_INPUT_PROCESSED| PROC_BOT

    %% LLM Phase
    subgraph "LLM Phase (llmProcessing)"
        PROC_BOT -->|Process Text| Bot
        Bot -->|Generate Response| BOT_OUT[Bot Response]
    end

    BOT_OUT -->|BOT_RESPONSE_RECEIVED| RESP

    %% TTS Phase
    subgraph "TTS Phase (textToSpeech)"
        RESP -->|Convert Text to Speech| TTS
        TTS -->|Generate Audio| TTS_OUT[Audio Data]
        TTS_OUT -->|RESPONSE_PREPARATION_COMPLETED| PLAY
        PLAY -->|Play Audio| Client
    end

    PLAY -->|PLAYBACK_COMPLETED| IDLE

    %% Barge-In Flow
    Client -->|Audio During Playback| BARGE[Barge-In Detection]
    BARGE -->|BARGE_IN_DETECTED| PROC_IN
    PLAY -->|Stop Playback| BARGE
```
> **Note:** For details on limitations and best practices in phase metrics tracking, see [performance-metrics.md](../src/services/monitoring/performance-metrics.md#limitations-edge-cases-and-enforcement).

## Key Aspects of the Flow

1. **Event-Driven Architecture**: The system uses events to trigger state transitions and component interactions.

2. **State Machine Core**: The state machine manages the flow through well-defined states (IDLE, PROCESSING_INPUT, PROCESSING_BOT, RESPONDING, PLAYING).

3. **Metrics Tracking**: The system tracks three key phases:
   - `speechToText`: From user speech to final transcript
   - `llmProcessing`: From transcript to bot response
   - `textToSpeech`: From bot response to audio playback completion

4. **Barge-In Handling**: The system can detect and handle user interruptions during playback, transitioning from PLAYING back to PROCESSING_INPUT.

5. **Component Interaction**: Components communicate through events rather than direct method calls, creating a decoupled architecture.

## Related Documentation

For more detailed information, see:
- [State Machine Documentation](../src/session/state-machine.md)
- [Session Service Documentation](../src/common/session.md)
- [Barge-In Documentation](./barge-in.md)
