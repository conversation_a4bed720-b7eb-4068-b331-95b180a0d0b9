# Barge-in and Stable Stream Architecture & Implementation

## Overview

This document describes the unified architecture and implementation plan for barge-in and stable stream logic in the AudioConnector Server. The goal is to improve maintainability, clarity, and extensibility by introducing a single `BargeInManager` responsible for both audio and DTMF barge-in detection, centralizing playback state, and using a minimal, callback-based interface.

---
## 2. Barge-In Metrics Flow

This section details how metrics phases are managed during a barge-in event, ensuring accurate tracking even when playback or bot response is interrupted.

### Step-by-Step Barge-In Metrics Flow

1. **Barge-In Detected During Playback or Responding**
   - `metricsAdapter.endPhase('textToSpeech')` is called to end the TTS phase early if playback is interrupted.
   - `metricsAdapter.endPhase('llmProcessing')` is also called to ensure the LLM phase is closed if still active (e.g., if barge-in occurs during bot response preparation).
2. **Transition to User Input**
   - The state machine transitions to PROCESSING_INPUT to handle new user input.
   - `metricsAdapter.startPhase('speechToText')` is called at the start of new user input processing.
3. **Final Transcript Received**
   - `metricsAdapter.endPhase('speechToText')` is called after the final transcript is processed.
4. **Bot Processing and Response**
   - `metricsAdapter.startPhase('llmProcessing')` is called when bot/LLM processing begins.
   - `metricsAdapter.endPhase('llmProcessing')` is called when the bot response is received.
5. **TTS Synthesis/Playback Starts**
   - `metricsAdapter.startPhase('textToSpeech')` is called at the start of TTS synthesis or playback for the new response.
6. **Playback Completes**
   - `metricsAdapter.endPhase('textToSpeech')` is called when playback completes.

### Special Considerations & Edge Cases

- If barge-in occurs before the previous phase (TTS or LLM) has ended, the phase is explicitly ended to avoid overlapping or missing metrics.
- All metrics calls are associated with the current requestId and phase type for accurate tracking.
- If multiple barge-ins occur in rapid succession, each interruption will end the current phase and start a new `speechToText` phase for the new input.

**Summary:**
The barge-in flow ensures that all metrics phases are properly closed and restarted as needed, providing a clear, non-overlapping record of each user interaction, even in the presence of interruptions.

---

## 1. Unified Manager: `BargeInManager`

### Responsibilities

- Detect barge-in events from both audio (speech) and DTMF sources.
- Centralize and expose playback state (`playing`, `paused`, `stopped`).
- Provide a simple callback-based API for barge-in, pause detection, and playback state changes.
- Manage configuration for barge-in and stable stream features.
- Decouple barge-in logic from session and playback handlers.

### Example Interface

```typescript
// src/services/barge-in/barge-in-manager.ts
type PlaybackState = 'playing' | 'paused' | 'stopped';

export class BargeInManager {
  setPlaybackState(state: PlaybackState): void {
    /* ... */
  }
  getPlaybackState(): PlaybackState {
    /* ... */
  }
  onBargeIn(cb: (event: BargeInEvent) => void): () => void {
    /* ... */
  }
  onPlaybackStateChange(cb: (state: PlaybackState) => void): () => void {
    /* ... */
  }
  detectAudioBargeIn(): void {
    /* ... */
  }
  detectDtmfBargeIn(): void {
    /* ... */
  }
}
```

---

## 2. Centralized Playback State

- Playback state (`playing`, `paused`, `stopped`) is managed by `BargeInManager`.
- All components (audio, DTMF, session) interact with playback state via the manager.
- No direct flag manipulation outside the manager.

### Example

```typescript
// Set playback state
bargeInManager.setPlaybackState('playing');

// Optionally handle state changes
bargeInManager.onPlaybackStateChange(state => {
  /* ... */
});
```

---

## 3. Minimal Callback-Based API

- All barge-in, pause detection, and playback state changes are handled via explicit callbacks.
- No event bus or observer pattern is used unless future extensibility is required.
- Consumers (session, metrics, etc.) provide callbacks when constructing the manager.

### Example

```typescript
const bargeInManager = new BargeInManager();
bargeInManager.onBargeIn(event => {
  /* handle barge-in */
});
bargeInManager.onPlaybackStateChange(state => {
  /* handle playback state change */
});
```

---

## 4. Configuration

### Retained Options (with defaults)

| Option                        | Default    | Description                                  |
| ----------------------------- | ---------- | -------------------------------------------- |
| ENABLE_BARGE_IN               | true       | Enable/disable barge-in                      |
| BARGE_IN_CONFIDENCE_THRESHOLD | 0.6        | Min confidence for speech barge-in           |
| ENABLE_STABLE_STREAM          | true       | Enable pause detection                       |
| PAUSE_THRESHOLD_MS            | 1500       | Pause duration for utterance finalization    |
| ASR_STREAMING_MODE            | 'standard' | ASR mode: 'standard', 'hybrid', 'continuous' |

Add to your `.env` file:

```
# Barge-in settings
ENABLE_BARGE_IN=true
BARGE_IN_CONFIDENCE_THRESHOLD=0.6

# Stable stream settings
ENABLE_STABLE_STREAM=true
PAUSE_THRESHOLD_MS=1500

# ASR Streaming Mode Configuration
# Options: 'standard', 'hybrid', or 'continuous'
ASR_STREAMING_MODE=standard
```

---

## 5. Architecture Diagram

```mermaid
flowchart TD
    subgraph Session
      S1(Session)
    end
    subgraph BargeInManager
      B1(BargeInManager)
    end
    subgraph Inputs
      A1(Audio Input)
      D1(DTMF Input)
    end
    subgraph Outputs
      O1(Bot Service)
      O2(Genesys Cloud)
      O3(Metrics/Logger)
    end
    subgraph StateMachine
      SM1(EnhancedStateManager)
      HBA(HandleBargeInAction)
    end

    A1 -- audio --> B1
    D1 -- dtmf --> B1
    S1 -- setPlaybackState --> B1
    B1 -- "onBargeIn callback" --> S1
    B1 -- "onPlaybackStateChange callback" --> S1
    S1 -- state transitions --> SM1
    SM1 -- exit actions --> HBA
    HBA -- cancel bot turn --> O1
    S1 -- user input --> O1
    B1 -- "barge-in event" --> O2
    B1 -- callbacks --> O3
```

## 5.1 State Machine Integration

The barge-in functionality is now tightly integrated with the state machine through a dedicated action:

### HandleBargeInAction

- Registered as an exit action for both RESPONDING and PLAYING states
- Centralizes barge-in handling logic in one place
- Ensures consistent behavior regardless of barge-in source
- Provides state-specific transitions:
  - During PLAYING state: Transitions to IDLE state
  - During RESPONDING state: Transitions to PROCESSING_INPUT state

### State Transition Flow

```mermaid
sequenceDiagram
    participant User
    participant AudioManager
    participant BargeInManager
    participant Session
    participant StateManager
    participant HandleBargeInAction

    Note over Session: In PLAYING or RESPONDING state
    User->>AudioManager: Speaks during playback
    AudioManager->>BargeInManager: detectAudioBargeIn(text)
    BargeInManager->>Session: onBargeIn callback(event)
    Session->>StateManager: setState(IDLE or PROCESSING_INPUT)
    StateManager->>HandleBargeInAction: execute(context)
    HandleBargeInAction->>Session: cancelCurrentTurn()
```

### Error Handling Improvements

The BargeInManager now includes improved error handling:

1. **Callback Error Logging**: Errors in callbacks are caught and logged
2. **Playback State Validation**: Only valid playback states are accepted
3. **State Transition Validation**: The state machine validates all transitions

For more detailed information about the barge-in implementation, see [src/services/barge-in/barge-in-implementation.md](../src/services/barge-in/barge-in-implementation.md).

## 6. Interruption and Preemption for Barge-In and Pause Detection

To guarantee real-time responsiveness, the barge-in and pause detection system now supports **interruption (preemption)** of ongoing state machine transitions.

### How It Works

- When a high-priority event (barge-in or pause detection) occurs, the system sets an `interruptionRequested` flag.
- The state machine checks this flag at safe points during transitions (e.g., after exit actions).
- If interruption is requested, the current transition is aborted or completed as quickly as possible, and the high-priority event is processed immediately.
- Only one interruption is processed at a time; further interruptions are ignored until the current one is handled.

### Why This Matters

- Ensures that user input (especially barge-in) is never lost or delayed, even if the system is busy with another transition.
- Keeps the implementation simple and maintainable: a single flag and minimal logic, no complex event queues.
- All actions and transitions are designed to be safely interruptible, with clear cleanup and rollback.

### Example

1. User speaks during playback (barge-in) or a pause is detected.
2. The system sets `interruptionRequested = true`.
3. After the current transition's exit actions, the state machine checks the flag.
4. If set, it aborts or completes the transition, then immediately processes the high-priority event (e.g., transitions to PROCESSING_INPUT).
5. The flag is cleared after the interruption is handled.

This approach ensures barge-in and pause detection are always responsive, without introducing unnecessary complexity.

---

---

## 6. Implementation Progress Checklist

- [x] Create `BargeInManager` class with callback-based API
- [x] Migrate audio and DTMF barge-in logic into `BargeInManager`
- [x] Centralize playback state in `BargeInManager`
- [x] Refactor consumers to use callbacks (remove direct handler attachment)
- [x] Improve error handling and validation in `BargeInManager`
- [x] Create dedicated state machine action for barge-in handling
- [x] Standardize state transitions for barge-in events
- [x] Update documentation (README, diagrams, implementation details)
- [ ] Add/Update unit and integration tests for new manager
- [x] Remove legacy managers and unused config
- [x] Validate end-to-end barge-in and pause detection

---

## 7. Testing

1. **Unit Test Barge-in Detection**:

   - Mock ASR service and verify barge-in events are triggered
   - Test with different confidence thresholds
   - Verify DTMF barge-in works correctly

2. **Unit Test Pause Detection**:

   - Mock transcript events with timestamps
   - Verify pause detection correctly identifies complete utterances
   - Test with different pause thresholds

3. **Integration Test**:
   - Test end-to-end conversation flow with barge-in
   - Verify stable stream processing with natural pauses
   - Verify Genesys Cloud receives barge-in events
   - Check metrics tracking for barge-in and pause detection

## 8. Barge-In Event Flow for Debugging

This section provides a step-by-step depiction of the barge-in flow, highlighting key checkpoints and what to verify at each stage for effective debugging.

### Step-by-Step Flow

1. **User Interrupts (Barge-In Trigger)**
   - **Checkpoint:** User speaks during playback or presses DTMF.
   - **Debug:** Confirm user input is actually received (audio/DTMF logs).

2. **BargeInManager Detects Barge-In**
   - **Checkpoint:** `detectAudioBargeIn` or `detectDtmfBargeIn` called.
   - **Debug:** Look for `[BargeInManager]` logs; check if detection is enabled and confidence threshold is met.

3. **Barge-In Event Emitted**
   - **Checkpoint:** `emitBargeIn` fires callbacks and emits `BARGE_IN_DETECTED` event.
   - **Debug:** Check for `logBargeIn` output and event emission in logs.

4. **Session Receives Barge-In Event**
   - **Checkpoint:** Session's barge-in handler invoked.
   - **Debug:** Look for `[Session] Handling barge-in...` and `[HandleBargeInAction]` logs.

5. **State Machine Interruption**
   - **Checkpoint:** State machine transitions from `PLAYING`/`RESPONDING` to `PROCESSING_INPUT`.
   - **Debug:** Check state transition logs; verify `interruptionRequested` flag if transitions are delayed.

6. **Exit Actions & Cleanup**
   - **Checkpoint:** `HandleBargeInAction` and other exit actions run.
   - **Debug:** Ensure playback/bot turn is cancelled; look for cleanup logs.

7. **Metrics Phases Updated**
   - **Checkpoint:** `metricsAdapter.endPhase('textToSpeech'/'llmProcessing')` and `startPhase('speechToText')` called.
   - **Debug:** Check metrics logs for correct phase closure and restart.

8. **Genesys Notification**
   - **Checkpoint:** `Session.sendBargeIn` sends event to Genesys Cloud.
   - **Debug:** Look for `[BARGE-IN] Sending barge-in event to Genesys Cloud` log and confirm event on Genesys side.

9. **Logging**
   - **Checkpoint:** All barge-in logs use `logBargeIn` (magenta color).
   - **Debug:** If logs are missing or wrong color, check logger configuration and log endpoints.

### Mermaid Sequence Diagram

```mermaid
sequenceDiagram
    participant User
    participant AudioManager
    participant BargeInManager
    participant Session
    participant StateManager
    participant HandleBargeInAction
    participant Metrics
    participant Genesys

    User->>AudioManager: Speaks/DTMF during playback
    AudioManager->>BargeInManager: detectAudioBargeIn/detectDtmfBargeIn
    BargeInManager->>Session: emitBargeIn (callback/event)
    Session->>StateManager: handleBargeInDetected
    StateManager->>HandleBargeInAction: execute exit actions
    HandleBargeInAction->>Session: cancelCurrentTurn/cleanup
    StateManager->>Metrics: endPhase('TTS'/'LLM'), startPhase('STT')
    Session->>Genesys: sendBargeIn (event message)
    Note over Session,Genesys: All steps logged with logBargeIn
```

### Debugging Tips

- **No barge-in detected?** Check if detection is enabled, thresholds, and input is reaching `BargeInManager`.
- **No state transition?** Check event emission, state machine subscriptions, and `interruptionRequested` logic.
- **Metrics not updated?** Ensure exit actions are running and metrics adapter is called.
- **No Genesys event?** Confirm `sendBargeIn` is called and message is sent on the wire.
- **Logs missing or wrong color?** Ensure all barge-in logs use `logBargeIn`.
---

---

## 9. Deferred Session Closure and Barge-In Integration

The AudioConnector Server implements a **deferred session closure mechanism** to guarantee that user input and bot responses are always processed to completion—even if a disconnect or close is requested during a barge-in event or while processing user input.

- **Critical States:** If a close/disconnect is requested while the session is in `PROCESSING_INPUT`, `PROCESSING_BOT`, or `RESPONDING`, the session does not close immediately. Instead, a `pendingClose` flag is set.
- **Post-Processing Check:** After the bot response is sent and the session transitions to a "safe" state (such as `IDLE` or `PLAYING`), the state machine checks the `pendingClose` flag. If set, the session transitions immediately to `DISCONNECTING` and then `CLOSED`.
- **Barge-In Compatibility:** If a user interrupts playback (barge-in) and provides new input, the session will process the new input and deliver the bot response before closing, even if a disconnect is requested during the interruption.
- **Responsiveness:** This mechanism ensures that barge-in events are handled immediately and that the user always receives a response, preserving both reliability and responsiveness.

This approach is fully compatible with the callback-driven `BargeInManager` and the state machine architecture, and is considered best practice for async, event-driven systems.

## 8. Summary

The AudioConnector Server now uses a unified, callback-driven `BargeInManager` to handle all barge-in and playback state logic, replacing the previous split and flag-based approach. Configuration is rationalized, and all consumers interact via explicit callbacks for maintainability and clarity. The plan includes a checklist, code snippets, and a Mermaid diagram for implementation guidance.
