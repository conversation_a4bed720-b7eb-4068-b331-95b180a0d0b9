# Services Overview

This document provides a high-level overview of all services in the application, their responsibilities, and how they interact with each other.

## Core Services

### Session Service

**Purpose**: Orchestrates the lifecycle of a voice interaction session, coordinating all other services.

**Key Components**:

- `Session`: Central coordinator for the session lifecycle
- `SessionStateManager`: Manages explicit session state and transitions
- `WebSocketController`: Handles WebSocket communication with clients

**Documentation**: [Session Service](../src/session/session-service.md)

### ASR Service

**Purpose**: Provides speech-to-text functionality, converting audio to transcripts.

**Key Components**:

- `BaseSpeechService`: Abstract base class for speech services
- `AzureSpeechService`: Azure Cognitive Services implementation
- `GoogleSpeechAdapter`: Google Cloud Speech implementation
- `EnhancedASRService`: Wrapper with additional functionality

**Documentation**: [ASR Service](../src/services/asr-service/asr-service.md)

### Audio Service

**Purpose**: Manages audio processing, playback, and integration with speech recognition.

**Key Components**:

- `AudioManager`: Handles audio processing and ASR integration
- `AudioServiceAdapter`: Adapts Session to audio functionality

**Documentation**: [Audio Service](../src/services/audio/audio-service.md)

### Bot Service

**Purpose**: Manages bot resources, processes user input, and generates responses using LLM technology.

**Key Components**:

- `BotService`: Manages bot instances
- `BotResource`: Handles interactions with LLM providers
- `BotServiceAdapter`: Adapts Session to bot functionality
- `LLMProvider`: Interface for different LLM implementations

**Documentation**: [Bot Service](../src/services/bot-service/bot-service.md)

### Speech Service

**Purpose**: Provides text-to-speech functionality, converting text to audio.

**Key Components**:

- `BaseTTSService`: Abstract base class for TTS services
- `AzureTTSService`: Azure Cognitive Services implementation
- `GoogleTTSService`: Google Cloud TTS implementation
- `ElevenLabsTTSService`: ElevenLabs implementation
- `ServiceContainer`: Factory system for creating services

**Documentation**: [Speech Service](../src/services/speech/speech-service.md)

## Supporting Services

### Barge-In Service

**Purpose**: Detects and manages user interruptions during audio playback.

**Key Components**:

- `BargeInManager`: Central manager for barge-in detection and playback state

**Documentation**: [Barge-In Service](../src/services/barge-in/barge-in-service.md)

### DTMF Service

**Purpose**: Processes DTMF digit input from clients and manages digit sequences.

**Key Components**:

- `DTMFManager`: High-level manager for DTMF processing
- `DTMFService`: Low-level service for digit accumulation

**Documentation**: [DTMF Service](../src/services/dtmf/dtmf-service.md)

### Transcript Service

**Purpose**: Handles transcript processing, deduplication, and barge-in flag management.

**Key Components**:

- `TranscriptProcessor`: Handles duplicate detection and barge-in flags

**Documentation**: [Transcript Service](../src/services/transcript/transcript-service.md)

### Monitoring Service

**Purpose**: Provides logging, metrics tracking, and performance monitoring.

**Key Components**:

- `PerformanceLogger`: Tracks performance metrics
- `SessionMetricsAdapter`: Adapts Session to metrics infrastructure
- `Logging System`: Structured logging with different levels

**Documentation**: [Monitoring Service](../src/services/monitoring/monitoring-service.md)

## Service Interactions

```mermaid
graph TD
    Session[Session Service] --> ASR[ASR Service]
    Session --> Audio[Audio Service]
    Session --> Bot[Bot Service]
    Session --> DTMF[DTMF Service]
    Session --> BargeIn[Barge-In Service]
    Session --> Transcript[Transcript Service]
    Session --> Monitoring[Monitoring Service]
    Session --> WebSocket[WebSocket Controller]

    Audio --> ASR
    Audio --> BargeIn

    Bot --> Speech[Speech Service]

    DTMF --> BargeIn

    subgraph "Core Services"
        Session
        ASR
        Audio
        Bot
        Speech
    end

    subgraph "Supporting Services"
        DTMF
        BargeIn
        Transcript
        Monitoring
        WebSocket
    end
```

## Service Lifecycle

1. **Session Creation**: A new Session is created when a client connects
2. **Service Initialization**: Session initializes all required services
3. **Audio Processing**: Audio data is processed through the ASR service
4. **Transcript Generation**: ASR generates transcripts from audio
5. **Bot Processing**: Bot service processes transcripts and generates responses
6. **Speech Synthesis**: Speech service converts text responses to audio
7. **Audio Playback**: Audio is sent back to the client
8. **Barge-In Handling**: Interruptions are detected and handled
9. **Session Termination**: Session is closed and resources are cleaned up

## Configuration

Most services can be configured through environment variables. See each service's documentation for specific configuration options.

## Extending the System

To add new functionality:

1. **New Provider**: Implement the appropriate interface and add to the factory system
2. **New Service**: Create a new service directory with implementation and documentation
3. **Service Integration**: Update the Session service to use the new functionality

## Common Patterns

The application uses several common patterns:

- **Adapter Pattern**: Used to adapt the Session to specialized services
- **Factory Pattern**: Used for creating provider-specific service instances
- **State Machine**: Used for explicit session state management
- **Event-Driven Architecture**: Used for asynchronous communication between services
- **Dependency Injection**: Used for providing dependencies to services
