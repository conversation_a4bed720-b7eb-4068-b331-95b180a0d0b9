# Testing Guide

This document provides an overview of the testing approach, structure, and best practices for the Genesys Cloud Audio Connector project.

## Testing Structure

Tests should be organized in `__tests__` directories collocated with the features they are testing. This structure follows the principle of keeping tests close to the code they verify, making it easier to maintain and understand the relationship between tests and implementation.

### Current Directory Structure

The project currently has a mix of test locations:

1. Tests in `__tests__` directories (preferred approach):

```
src/
├── services/
│   ├── asr-service/
│   │   └── __tests__/
│   ├── dtmf/
│   │   └── __tests__/
│   │       ├── dtmf-manager.test.ts
│   │       └── dtmf-service.test.ts
│   └── monitoring/
│       └── __tests__/
│           ├── logging-flood-protection.test.ts
│           ├── metrics-integration.test.ts
│           ├── performance-logger.test.ts
│           └── performance-metrics-store.test.ts
└── session/
    └── state-machine/
        └── __tests__/
            ├── playing-state-actions.test.ts
            ├── processing-bot-state-actions.test.ts
            ├── processing-input-state-actions.test.ts
            └── processing-responding-state-actions.test.ts
```

2. Tests directly in the same directory as the implementation:

```
src/
├── services/
│   ├── audio/
│   │   └── audio-service-adapter.test.ts
│   └── barge-in/
│       └── barge-in-manager.test.ts
```

3. Tests in a separate `tests` directory:

```
src/
└── session/
    └── state-machine/
        └── tests/
            ├── disconnecting-and-closed-state-actions.test.ts
            ├── idle-state-actions.test.ts
            ├── initialize-asr-service-action.test.ts
            ├── playing-state-actions.test.ts
            ├── process-bot-start-action.test.ts
            ├── processing-bot-state-actions.test.ts
            ├── processing-input-state-actions.test.ts
            ├── processing-responding-state-actions.test.ts
            ├── set-conversation-id-action.test.ts
            ├── set-playback-state-action.test.ts
            ├── test-types.ts
            └── test-utils.ts
```

### Recommended Structure

All tests should be moved to `__tests__` directories collocated with the code they test. This provides consistency and makes it easier to find tests for specific components.

## Test Utilities

The project includes several test utilities to facilitate testing:

### Mock Objects

- `createMockSession()`: Creates a mock session implementing the `ISession` interface
- `createMockStateContext()`: Creates a mock state context for testing state machine actions
- `createMockAction()`: Creates a mock state action for testing

These utilities are located in `src/session/state-machine/__tests__/test-utils.ts`.

### Test Types

The project includes test-specific types in `src/session/state-machine/tests/test-types.ts`:

- `TestBotResponse`: A simplified bot response class for tests
- `TestTTSService`: A simplified TTS service for tests

## Jest Configuration

The project uses Jest for testing. The configuration is defined in `jest.config.js`:

```javascript
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  testMatch: ['**/__tests__/**/*.test.ts'],
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
    '!src/**/__tests__/**',
    '!src/**/__mocks__/**',
    '!src/__version.ts',
    '!src/index.ts',
  ],
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70,
    },
  },
  moduleNameMapper: {
    '^src/(.*)$': '<rootDir>/src/$1',
  },
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  globals: {
    'ts-jest': {
      tsconfig: 'tsconfig.test.json',
    },
  },
};
```

### Key Configuration Points

1. **Test Pattern**: Currently only matches tests in `__tests__` directories

   - `testMatch: ['**/__tests__/**/*.test.ts']`
   - This excludes tests in other locations like `src/session/state-machine/tests/`

2. **Coverage Thresholds**: Set to 70% for branches, functions, lines, and statements

   - This ensures a minimum level of test coverage

3. **Setup File**: Uses `jest.setup.js` for global test configuration
   - Sets environment variables for testing
   - Increases test timeout to 10 seconds
   - Silences console output during tests

### Recommended Configuration Updates

To improve the test configuration:

1. **Update Test Pattern**: Include all test files regardless of location

   ```javascript
   testMatch: ['**/__tests__/**/*.test.ts', '**/*.test.ts'],
   ```

2. **Add Timeout Configuration**: Increase the default timeout for slow tests

   ```javascript
   // In jest.setup.js
   jest.setTimeout(30000); // 30 seconds
   ```

3. **Add Error Handling**: Configure Jest to fail on unhandled promise rejections
   ```javascript
   // In jest.setup.js
   process.on('unhandledRejection', reason => {
     console.error('Unhandled Promise Rejection:', reason);
     process.exit(1);
   });
   ```

## Running Tests

Tests are run using Jest. The following commands are available:

```bash
# Run all tests
yarn test

# Run tests in watch mode
yarn test:watch

# Run tests with coverage
yarn test:coverage

# Run specific tests for monitoring services
yarn test:metrics

# Run tests with increased timeout
yarn test --testTimeout=30000

# Run tests and detect open handles
yarn test --detectOpenHandles

# Run a specific test file
yarn test path/to/test.ts

# Run tests matching a specific name
yarn test -t "test name pattern"
```

## Known Issues and Performance Considerations

### Duplicate Test Files

There are duplicate test files in different directories:

- `src/session/state-machine/__tests__/` (older tests)
- `src/session/state-machine/tests/` (newer tests)

This duplication should be resolved by consolidating tests into the `__tests__` directories.

### Performance Issues

The test suite currently takes a long time to run (over 100 seconds). Specific issues include:

1. **Timeouts**: Some tests have long timeouts or hanging promises

   - The `process-bot-start-action.test.ts` has a timeout that causes the test to hang
   - Several tests in `performance-logger.test.ts` take a long time to complete

2. **Asynchronous Code**: Improper handling of async operations

   - Some tests don't properly await promises or handle rejections
   - The `metrics-integration.test.ts` has issues with async operations

3. **Event Listeners**: Unclosed event listeners or unresolved promises
   - The state machine tests often leave event listeners active
   - Some tests don't clean up resources properly

To identify slow tests, run:

```bash
yarn test --verbose
```

### Specific Test Issues

1. **performance-logger.test.ts**:

   - Missing mock methods: `setUserInput` and `setAiReply`
   - Incorrect parameter expectations in `initializeRequest`

2. **metrics-integration.test.ts**:

   - Type errors with `setUserInput` and `setAiReply` methods
   - Failing fluent API tests

3. **processing-bot-state-actions.test.ts**:
   - Hanging due to unresolved promises
   - TypeError: "Cannot read properties of undefined (reading 'getState')"
   - Outdated expectations for state transitions

### Common Test Failures

1. **Missing Mock Methods**: Tests may fail if mock objects don't implement all required methods
2. **Outdated Expectations**: Tests may have expectations that don't match the current implementation
3. **Type Errors**: TypeScript errors due to interface changes

## Best Practices

1. **Collocate Tests**: Keep tests in `__tests__` directories next to the code they test
2. **Mock Dependencies**: Use mock objects to isolate the code being tested
3. **Test One Thing**: Each test should focus on testing one specific behavior
4. **Clean Up**: Ensure all async operations are properly cleaned up
5. **Use Test Utilities**: Leverage the provided test utilities for consistent testing
6. **Descriptive Names**: Use descriptive test names that explain what is being tested
7. **Avoid Timeouts**: Minimize the use of timeouts in tests

## Test Coverage

The project aims for high test coverage, especially for critical components like:

- State machine actions
- Session management
- Audio processing
- Event handling

Run `yarn test:coverage` to generate a coverage report.

## Recommended Test Migration Approach

To improve the test suite, the following steps are recommended:

1. **Consolidate Test Locations**:

   - Move all tests to `__tests__` directories collocated with the code they test
   - Ensure test utilities are accessible to all tests

2. **Fix Common Issues**:

   - Update mock objects to implement all required methods
   - Fix incorrect expectations
   - Ensure proper cleanup of async operations

3. **Improve Test Performance**:

   - Remove unnecessary timeouts
   - Properly handle promises and async operations
   - Clean up event listeners and resources

4. **Prioritize Tests**:
   - Focus on critical components first
   - Consider skipping or fixing flaky tests

### Migration Plan

1. **Phase 1: Test Structure**

   - Create `__tests__` directories where missing
   - Move tests from `tests` directories to `__tests__`
   - Move standalone test files into `__tests__` directories

2. **Phase 2: Fix Common Issues**

   - Update mock objects in test utilities
   - Fix type errors and missing methods
   - Update expectations to match current implementation

3. **Phase 3: Performance Improvements**
   - Identify and fix slow tests
   - Add proper cleanup for async operations
   - Remove or fix tests with timeouts

## Troubleshooting

### Tests Hanging

If tests are hanging, try running with the `--detectOpenHandles` flag:

```bash
yarn test --detectOpenHandles
```

This will help identify resources that weren't properly closed.

You can also run tests with a timeout:

```bash
yarn test --testTimeout=10000
```

### TypeScript Errors

If you encounter TypeScript errors in tests:

1. Check if interfaces have changed
2. Update mock objects to implement new methods
3. Ensure test utilities are up to date

### Slow Tests

For slow tests:

1. Check for unnecessary timeouts
2. Ensure promises are properly resolved
3. Look for infinite loops or recursive calls
4. Verify that event listeners are properly removed

### Running Individual Tests

To run a single test file:

```bash
yarn test path/to/test.ts
```

To run a specific test within a file:

```bash
yarn test -t "test name pattern"
```
