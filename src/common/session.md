# Session Service: Event-Driven Architecture

## Purpose

The Session service orchestrates the lifecycle of a voice interaction session using an event-driven architecture. It coordinates audio, speech recognition, bot logic, DTMF input, barge-in, and communication with clients over WebSocket. This design emphasizes decoupled components, clear event flows, robust error handling, and extensibility.

---

## Key Responsibilities

- Manage the full lifecycle of a voice session through events, from initialization to disconnect.
- Coordinate components through a central event system rather than direct method calls.
- Maintain explicit session state driven by events for observability and debugging.
- Handle all client communication via WebSocket, translating between WebSocket messages and internal events.
- Ensure robust error handling and resource cleanup through error events.

---

## Core Components and Their Roles

### EventEmitter (`src/services/events/event-emitter.ts`)

- Central event bus for all component communication.
- Provides type-safe event subscription and emission.
- Supports event priorities to ensure critical events (like barge-in) are processed first.
- Handles error catching and logging for event listeners.

### Session (`src/common/session.ts`)

- Initializes and coordinates the event-driven session lifecycle.
- Subscribes to relevant events from all components.
- Maintains session context and configuration.
- Translates between external messages and internal events.

### EnhancedSessionStateManager (`src/session/state-machine/enhanced-state-manager.ts`)

- Subscribes to events that should trigger state transitions.
- Maintains the current state and validates transitions.
- Executes entry and exit actions for states.
- Emits state change events that other components can subscribe to.

### AudioManager (`src/services/audio/audio-manager.ts`)

- Manages audio input/output streams.
- Emits events for audio data, playback state changes, and errors.
- Subscribes to events that affect audio processing.

### DTMFManager (`src/services/dtmf/dtmf-manager.ts`)

- Processes DTMF digit input from the client.
- Emits events for DTMF input and completion.
- Subscribes to events that affect DTMF processing.

### BargeInManager (`src/services/barge-in/barge-in-manager.ts`)

- Detects barge-in from audio and DTMF.
- Emits high-priority barge-in events.
- Subscribes to playback state events.

### BotService (`src/services/bot-service/`)

- Processes user input and generates responses.
- Emits events for bot responses and errors.
- Subscribes to events that trigger bot processing.

### ASRService (`src/services/asr-service/`)

- Provides speech-to-text functionality.
- Emits events for transcripts, recognition state changes, and errors.
- Subscribes to events that affect ASR processing.

### PerformanceLogger (`src/services/monitoring/performance-logger.ts`)

- Subscribes to all relevant events to track metrics.
- Emits events for performance data and logging.

### WebSocketController (`src/session/websocket-controller.ts`)

- Translates WebSocket messages to internal events and vice versa.
- Emits events for client messages and connection state changes.
- Subscribes to events that should be sent to the client.

---

## Event-Driven Session Lifecycle

1. **Initialization**: Session is created and emits a `SESSION_INITIALIZING` event, which triggers component initialization.
2. **Idle/Ready**: When initialization completes, a `SESSION_INITIALIZED` event is emitted, transitioning to IDLE state.
3. **Audio/DTMF Input**: AudioManager and DTMFManager emit `AUDIO_DATA` and `DTMF_INPUT` events.
4. **ASR Processing**: ASRService subscribes to `AUDIO_DATA` events and emits `TRANSCRIPT` events.
5. **User Input Processing**: The system emits a `USER_INPUT_RECEIVED` event when speech or DTMF input is recognized.
6. **Bot Interaction**: BotService subscribes to `USER_INPUT_PROCESSED` events and emits `BOT_RESPONSE_RECEIVED` events.
7. **Playback**: AudioManager subscribes to `BOT_RESPONSE_RECEIVED` events and emits `PLAYBACK_STARTED` and `PLAYBACK_COMPLETED` events.
8. **Barge-In**: BargeInManager emits high-priority `BARGE_IN_DETECTED` events when interruptions are detected.
9. **Error Handling**: Components emit `ERROR` events which are handled centrally.
10. **Session End**: A `SESSION_CLOSE_REQUESTED` event triggers the cleanup process, followed by a `SESSION_CLOSED` event.

## Event-Driven State Management

### Event-Based State Transitions

In the event-driven architecture, state transitions are triggered by events rather than direct method calls:

- **Event Emission:** Components emit events to signal that something has happened.
- **Event Subscription:** The state manager subscribes to events that should trigger state transitions.
- **Decoupled Components:** Components don't need to know about the state machine; they just emit events.
- **Centralized Control:** The state manager is the only component that can change state, but it responds to events from various sources.

### Event Flow for Initial Greeting

For the initial greeting:

1. The session emits an `INITIAL_GREETING_REQUESTED` event.
2. The bot service subscribes to this event, processes it, and emits a `BOT_RESPONSE_RECEIVED` event.
3. The state manager subscribes to the `BOT_RESPONSE_RECEIVED` event and transitions to the appropriate state.
4. The state change triggers entry/exit actions and emits a `STATE_CHANGED` event.

### Benefits of Event-Driven Approach

The event-driven approach addresses several issues with the previous direct-call approach:

- **No Race Conditions:** Events are processed sequentially, preventing multiple simultaneous state transitions.
- **Clear Flow:** The event flow is explicit and easy to trace through logs.
- **Improved Testability:** Events can be mocked for testing, making components easier to test in isolation.
- **Better Error Handling:** Errors can be emitted as events and handled consistently.

For more details on the event-driven state machine, see [src/session/state-machine.md](../session/state-machine.md).

---

## Design Principles

- **Event-Driven Architecture**: Components communicate through events, not direct method calls.
- **Decoupled Components**: Each component can operate independently, emitting and subscribing to events.
- **Type-Safe Events**: Events are strongly typed for better developer experience and error prevention.
- **Prioritized Event Processing**: Critical events (like barge-in) are processed before lower-priority events.
- **Consistent Error Handling**: Errors are emitted as events and handled centrally.
- **Metrics and Observability**: Events provide natural points for metrics collection and logging.
- **Extensibility**: New components can subscribe to existing events without modifying existing code.

---

## File/Folder Structure

- `src/services/events/event-emitter.ts` – Central event system
- `src/services/events/session-events.ts` – Event type definitions
- `src/common/session.ts` – Session class (event coordinator)
- `src/session/state-machine/enhanced-state-manager.ts` – Event-driven state management
- `src/session/websocket-controller.ts` – WebSocket to event translation
- `src/services/audio/audio-manager.ts` – Audio event handling
- `src/services/dtmf/dtmf-manager.ts` – DTMF event handling
- `src/services/barge-in/barge-in-manager.ts` – Barge-in event emission
- `src/services/bot-service/` – Bot logic and event handling
- `src/services/asr-service/` – ASR event handling
- `src/services/monitoring/performance-logger.ts` – Event-based metrics

> **Recommended:** Consider organizing files by feature rather than by type, with each feature folder containing all related components, events, and documentation.

---

## Main Benefits of Event-Driven Architecture

- **Reduced Coupling**: Components interact through events, not direct method calls.
- **Improved Testability**: Events can be mocked for testing, making components easier to test in isolation.
- **Better Error Handling**: Events can include error information, making error propagation more consistent.
- **Enhanced Extensibility**: New components can subscribe to existing events without modifying existing code.
- **Clearer Intent**: Events clearly communicate the intent of state transitions.
- **Natural Interruption Model**: Events provide a natural way to model interruptions like barge-in.
- **Simplified Flow Control**: Event priorities ensure critical events are processed first.
- **Better Debugging**: Event flows are easier to trace and debug.

---

**Location:** `src/common/session.md`
