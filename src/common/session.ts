import { v4 as _uuid } from 'uuid';
import { WebSocket } from 'ws';
import { JsonStringMap, MediaParameter } from '../protocol/core';
import { PerformanceLogger, RequestContext } from '../services/monitoring';
import {
  ClientMessage,
  DisconnectParameters,
  DisconnectReason,
  EventParameters,
  SelectParametersForType,
  ServerMessage,
  ServerMessageBase,
  ServerMessageType,
} from '../protocol/message';
import {
  BotTurnDisposition,
  EventEntityBargeIn,
  EventEntityBotTurnResponse,
} from '../protocol/voice-bots';
import { MessageHandlerRegistry } from '../websocket/message-handlers/message-handler-registry';
import { BotService, BotResource } from '../services/bot-service/index';
import { getASRService } from '../services/asr-service';
import { BaseSpeechService, Transcript } from '../services/speech';
import { AudioManager } from '../services/audio/audio-manager';
import { DTMFManager } from '../services/dtmf/dtmf-manager';
import { BargeInManager, BargeInEvent } from '../services/barge-in/barge-in-manager';
import {
  logInfo,
  logMetrics,
  logWarning,
  logError,
  logDebug as _logDebug,
  logBargeIn,
} from '../services/logging';

import type { PlaybackState } from '../services/barge-in/barge-in-manager';

export class Session {
  /**
   * Flag to allow the first transcript after a barge-in to bypass duplicate detection.
   * This ensures that the first transcript after a barge-in is always processed,
   * even if it matches the previous transcript.
   */
  private justHadBargeIn = false;
  private MAXIMUM_BINARY_MESSAGE_SIZE = 64000;
  private disconnecting = false;
  private closed = false;
  private ws: WebSocket;
  private performanceLogger = PerformanceLogger.getInstance();
  private currentRequest: RequestContext | null = null;

  private messageHandlerRegistry = new MessageHandlerRegistry();
  private botService: BotService;
  private asrService: BaseSpeechService | null = null;
  private dtmfManager: DTMFManager;
  // These variables are used by the framework but appear unused to the linter
  // They should be kept for future compatibility
  /* eslint-disable @typescript-eslint/no-unused-vars */
  private _url: string;
  private ani: string | undefined;
  private clientSessionId: string;
  private conversationId: string | undefined;
  private lastServerSequenceNumber = 0;
  private audioManager: AudioManager;
  private lastClientSequenceNumber = 0;
  private startTime: Date;
  private _inputVariables: JsonStringMap = {};
  private _selectedMedia: MediaParameter | undefined;
  /* eslint-enable @typescript-eslint/no-unused-vars */
  private selectedBot: BotResource | null = null;
  // Flag to prevent duplicate responses
  private isProcessingResponse = false;
  private currentAiReply = '';
  // Track the last processed transcript to prevent duplicates
  private lastProcessedTranscript = '';
  private lastProcessedTimestamp = 0;

  private bargeInManager: BargeInManager;
  private stateManager: any; // Using 'any' type to bypass type checking

  constructor(ws: WebSocket, sessionId: string, url: string, botService: BotService) {
    this.ws = ws;
    this.clientSessionId = sessionId;
    this._url = url;
    this.botService = botService;
    this.startTime = new Date();

    // Initialize BargeInManager with the shared event emitter
    this.bargeInManager = new BargeInManager({ eventEmitter: this.eventEmitter });
    logInfo('[Session] BargeInManager initialized with shared event emitter');

    // Register barge-in callback for audio and DTMF events
    this.bargeInManager.onBargeIn((event: BargeInEvent) => {
      if (event.type === 'audio') {
        this.handleBargeIn('speech', event.text);
      } else if (event.type === 'dtmf') {
        this.handleBargeIn('dtmf');
      }
    });

    // Initialize AudioManager with callbacks
    this.audioManager = new AudioManager(
      this.handleTranscript.bind(this),
      // Pass the text to the BargeInManager for audio barge-in
      (_source, text) => this.bargeInManager.detectAudioBargeIn(text),
      error => this.handleError(error),
      this.stateManager,
      this.bargeInManager
    );
    this.audioManager.initialize().catch((err: any) => {
      logError(
        `[AudioManager] Initialization error: ${err instanceof Error ? err.message : String(err)}`
      );
    });

    // Initialize DTMFManager with callbacks, routing DTMF barge-in through BargeInManager
    this.dtmfManager = new DTMFManager(
      digits => this.handleDTMFComplete(digits),
      (_source, _digit) => this.bargeInManager.detectDtmfBargeIn(),
      this.bargeInManager
    );
  }

  // Getter methods for private properties
  getAni(): string | undefined {
    return this.ani;
  }

  getConversationId(): string | undefined {
    return this.conversationId;
  }

  getStartTime(): Date {
    return this.startTime;
  }

  close() {
    if (this.closed) {
      return;
    }

    // Dispose of ASR service if present
    if (this.asrService) {
      try {
        // Call dispose, but don't await since close() is sync
        Promise.resolve(this.asrService.dispose()).catch(err => {
          logError(
            `[ASR] Error disposing ASR service: ${err instanceof Error ? err.message : String(err)}`
          );
        });
      } catch (err) {
        logError(
          `[ASR] Error disposing ASR service: ${err instanceof Error ? err.message : String(err)}`
        );
      }
    }

    try {
      this.ws.close();
    } catch {}

    this.closed = true;
  }

  setConversationId(conversationId: string) {
    this.conversationId = conversationId;
    this.performanceLogger.setConversationId(conversationId);
  }

  setClientAni(ani: string | undefined) {
    this.ani = ani;
  }

  setInputVariables(inputVariables: JsonStringMap) {
    this._inputVariables = inputVariables;
  }

  setSelectedMedia(selectedMedia: MediaParameter) {
    this._selectedMedia = selectedMedia;
  }

  // Playback state is now managed by BargeInManager
  public setPlaybackState(state: PlaybackState) {
    this.bargeInManager.setPlaybackState(state);
  }

  // Handle transcript callback from AudioManager
  private handleTranscript(transcript: Transcript, isFinal: boolean): void {
    logInfo(
      `[melme] handleTranscript ENTRY. isFinal: ${isFinal}, transcript: "${transcript.text}"`
    );
    logInfo(
      `[Session] handleTranscript called. isFinal: ${isFinal}, transcript: "${transcript.text}"`
    );
    if (isFinal) {
      // Get the ASR streaming mode from environment
      const asrStreamingMode = process.env.ASR_STREAMING_MODE || 'standard';

      // Log the final transcript for debugging
      logInfo(
        `[Session] Received final transcript from AudioManager: "${transcript.text}" (mode: ${asrStreamingMode})`
      );

      // Check if we're already processing a response
      if (this.isProcessingResponse) {
        logInfo(
          `[Session] Already processing a response, ignoring final transcript: "${transcript.text}"`
        );
        return;
      }

      // Update metrics tracking
      this.currentRequest?.setUserInput(transcript.text);

      logInfo(`[Session] About to call processBotResponse with transcript: "${transcript.text}"`);
      // Process the bot response
      try {
        this.processBotResponse(transcript);
      } catch (err) {
        logError(
          `[Session] Error in processBotResponse: ${
            err instanceof Error ? err.message : String(err)
          }`
        );
      }
    }
  }

  /**
   * Barge-in Functionality
   *
   * Handles interruptions while the bot is speaking via speech or DTMF input.
   *
   * Flow:
   * 1. Input detected during audio playback
   * 2. Bot turn cancelled via BotResource.cancelCurrentTurn()
   * 3. Metrics tracked for the interrupted turn
   * 4. Barge-in event sent to Genesys Cloud to stop audio playback
   * 5. Local state updated and request context cleared
   *
   * @param source The source of the barge-in ('speech' or 'dtmf')
   * @param input The input that triggered the barge-in (speech text or DTMF digit)
   * @private
   */
  private handleBargeIn(source: 'speech' | 'dtmf', input?: string): void {
    // Set flag so the next transcript bypasses duplicate detection
    this.justHadBargeIn = true;
    // Only consider it a barge-in if audio is currently playing
    if (this.bargeInManager.getPlaybackState() !== 'playing') {
      return; // Not a barge-in if not playing audio
    }

    // Log the barge-in event for metrics tracking
    const bargeInMessage = `[BARGE-IN] Detected from ${source}${input ? ': ' + input : ''}`;
    logInfo(bargeInMessage);
    logMetrics(bargeInMessage);

    // Send barge-in event to Genesys Cloud to stop audio playback
    this.sendBargeIn();

    // Update state immediately to stop local audio processing
    this.bargeInManager.setPlaybackState('stopped');

    // Cancel any in-progress or pending bot responses for this turn
    // This prevents partial responses from being processed after interruption
    if (this.selectedBot) {
      this.selectedBot.cancelCurrentTurn();
    }

    // Track metrics for barge-in before clearing the request
    if (this.currentRequest) {
      // Set user input for metrics
      this.currentRequest.setUserInput(`Barge-in: ${source}${input ? ' - ' + input : ''}`);

      // If we have a current AI reply, mark it as interrupted
      if (this.currentAiReply) {
        this.currentRequest.setAiReply(`${this.currentAiReply} [Interrupted by barge-in]`);
      }

      // Finalize the current request to ensure metrics are properly recorded
      this.currentRequest.finalize();
    }

    // Clear local session state to avoid stale context
    this.currentRequest = null;
    this.currentAiReply = '';

    // Reset transcript tracking to prevent duplicate detection issues after barge-in
    // Use a longer timestamp to ensure we don't process the same input again
    this.lastProcessedTranscript = input || '';
    this.lastProcessedTimestamp = Date.now();

    // CRITICAL FIX: Reset the processing flag to allow new responses after barge-in
    this.isProcessingResponse = false;
    logInfo('[BARGE-IN] Reset session state to allow new responses');

    // Process the input text if available
    if (input && input.trim().length > 0 && this.selectedBot) {
      logBargeIn(`[BARGE-IN] Processing input text from barge-in: "${input}"`);

      // Create a transcript object for the barge-in text
      const transcript: Transcript = {
        text: input,
        confidence: 1.0,
      };

      // Create a new request for this barge-in input
      this.currentRequest = this.performanceLogger.createRequest();
      this.currentRequest.setUserInput(input);

      // Process the bot response with the barge-in text
      // Use a small delay to ensure barge-in event is processed first
      setTimeout(() => {
        this.processBotResponse(transcript);
      }, 200);
    }
  }

  private handleDTMFComplete(digits: string): void {
    // Handle completed DTMF sequence by processing as a bot input
    if (this.currentRequest) {
      this.currentRequest.setUserInput(`DTMF: ${digits}`);
      // Synthesize a Transcript object for DTMF input
      const transcript = { text: `DTMF: ${digits}`, confidence: 1.0 };
      // Use processBotResponse to handle DTMF as user input
      void this.processBotResponse(transcript as Transcript);
    }
  }

  // Handle error callback from AudioManager
  private handleError(error: Error | string): void {
    logError(`[AudioManager] Error: ${error instanceof Error ? error.message : String(error)}`);
    // Additional error handling logic can be added here
  }

  processTextMessage(data: string) {
    if (this.closed) {
      return;
    }

    const message = JSON.parse(data);

    logInfo(`Received a ${message.type} message.`);

    if (message.seq !== this.lastClientSequenceNumber + 1) {
      logWarning(`Invalid client sequence number: ${message.seq}.`);
      this.sendDisconnect('error', 'Invalid client sequence number.', {});
      return;
    }

    this.lastClientSequenceNumber = message.seq;

    if (message.serverseq > this.lastServerSequenceNumber) {
      logWarning(`Invalid server sequence number: ${message.serverseq}.`);
      this.sendDisconnect('error', 'Invalid server sequence number.', {});
      return;
    }

    if (message.id !== this.clientSessionId) {
      logWarning(`Invalid Client Session ID: ${message.id}.`);
      this.sendDisconnect('error', 'Invalid ID specified.', {});
      return;
    }

    const handler = this.messageHandlerRegistry.getHandler(message.type);

    if (!handler) {
      logWarning(`Cannot find a message handler for '${message.type}'.`);
      return;
    }

    handler.handleMessage(message as ClientMessage, this as any);
  }

  createMessage<Type extends ServerMessageType, Message extends ServerMessage>(
    type: Type,
    parameters: SelectParametersForType<Type, Message>
  ): ServerMessage {
    const message: ServerMessageBase<Type, typeof parameters> = {
      id: this.clientSessionId as string,
      version: '2',
      seq: ++this.lastServerSequenceNumber,
      clientseq: this.lastClientSequenceNumber,
      type,
      parameters,
    };

    return message as ServerMessage;
  }

  send(message: ServerMessage) {
    if (this.closed) {
      logWarning('[Session] Attempted to send message after session was closed.');
      return;
    }

    // Enhanced logging for events, especially barge-in events
    if (message.type === 'event') {
      const entityType = message.parameters.entities[0]?.type;

      // Special handling for barge-in events
      if (entityType === 'barge_in') {
        logBargeIn(`[WebSocket] Sending barge_in event message (seq: ${message.seq})`);
      } else {
        logInfo(`[WebSocket] Sending ${message.type} message: ${entityType} (seq: ${message.seq})`);
      }
    } else {
      logInfo(`[WebSocket] Sending ${message.type} message (seq: ${message.seq})`);
    }

    // Send the message
    const messageJson = JSON.stringify(message);
    this.ws.send(messageJson);

    // Log WebSocket state after sending
    logInfo(`[WebSocket] Message sent, WebSocket state: ${this.getWebSocketStateString()}`);
  }

  // Helper method to get WebSocket state as a string
  private getWebSocketStateString(): string {
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING:
        return 'CONNECTING';
      case WebSocket.OPEN:
        return 'OPEN';
      case WebSocket.CLOSING:
        return 'CLOSING';
      case WebSocket.CLOSED:
        return 'CLOSED';
      default:
        return `UNKNOWN (${this.ws.readyState})`;
    }
  }

  async sendAudio(bytes: Uint8Array) {
    if (this.closed) {
      logWarning('[Session] Attempted to send audio after session was closed.');
      return;
    }
    this.bargeInManager.setPlaybackState('playing'); // Set playback state before sending

    try {
      logInfo(`[Audio] Sending ${bytes.length} bytes of TTS audio data`);

      // Check WebSocket state before sending
      if (this.ws.readyState !== this.ws.OPEN) {
        throw new Error(`WebSocket is not open: readyState ${this.ws.readyState}`);
      }

      if (bytes.length <= this.MAXIMUM_BINARY_MESSAGE_SIZE) {
        await this.sendAudioChunk(bytes);
      } else {
        let currentPosition = 0;
        while (currentPosition < bytes.length) {
          // Recheck WebSocket state before each chunk
          if (this.ws.readyState !== this.ws.OPEN) {
            throw new Error(`WebSocket is not open: readyState ${this.ws.readyState}`);
          }

          const sendBytes = bytes.slice(
            currentPosition,
            currentPosition + this.MAXIMUM_BINARY_MESSAGE_SIZE
          );
          await this.sendAudioChunk(sendBytes);
          currentPosition += this.MAXIMUM_BINARY_MESSAGE_SIZE;

          // Add small delay between chunks to prevent overwhelming the connection
          await new Promise(resolve => setTimeout(resolve, 10));
        }
      }

      // If we have a current request, end the textToSpeech phase and finalize the request
      if (this.currentRequest) {
        // End the textToSpeech phase if it's still active
        try {
          // Create a new phase context and immediately end it
          // This is safer than trying to track if the phase is already ended
          const ttsPhase = this.currentRequest.trackPhase('textToSpeech');
          ttsPhase.end();
        } catch (err) {
          // Ignore errors - the phase might already be ended
        }

        // Finalize the request
        this.currentRequest.finalize();

        // Clear the current request
        this.currentRequest = null;
      }

      logInfo(`[Audio] TTS audio sent (${bytes.length} bytes) - waiting for PlaybackStarted event`);
    } catch (error) {
      logError(`Error sending audio: ${error instanceof Error ? error.message : String(error)}`);
      // Reset playback state on error
      this.bargeInManager.setPlaybackState('stopped');
      throw error;
    }
  }

  private async sendAudioChunk(chunk: Uint8Array): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      this.ws.send(chunk, { binary: true }, error => {
        if (error) {
          reject(error);
        } else {
          resolve();
        }
      });
    });
  }

  sendBargeIn() {
    const bargeInEvent: EventEntityBargeIn = {
      type: 'barge_in',
      data: {},
    };
    const message = this.createMessage('event', {
      entities: [bargeInEvent],
    } as SelectParametersForType<'event', EventParameters>);

    // Send the barge-in event to Genesys Cloud
    logBargeIn('[BARGE-IN] Sending barge-in event to Genesys Cloud');
    this.send(message);
  }

  sendTurnResponse(
    disposition: BotTurnDisposition,
    text: string | undefined,
    confidence: number | undefined
  ) {
    const botTurnResponseEvent: EventEntityBotTurnResponse = {
      type: 'bot_turn_response',
      data: {
        disposition,
        text,
        confidence,
      },
    };
    const message = this.createMessage('event', {
      entities: [botTurnResponseEvent],
    } as SelectParametersForType<'event', EventParameters>);

    this.send(message);
  }

  sendDisconnect(reason: DisconnectReason, info: string, outputVariables: JsonStringMap) {
    this.disconnecting = true;

    // Finalize conversation metrics if this is a normal completion
    if (reason === 'completed') {
      this.performanceLogger.finalizeConversation();
    }

    const disconnectParameters: DisconnectParameters = {
      reason,
      info,
      outputVariables,
    };
    const message = this.createMessage('disconnect', disconnectParameters);
    logInfo(`Sending disconnect message: ${JSON.stringify(message)}`);
    this.send(message);
  }

  sendClosed() {
    const message = this.createMessage('closed', {});
    this.send(message);
  }

  /*
   * Check if a bot exists for this session based on connection URL and input variables
   */
  async checkIfBotExists(): Promise<boolean> {
    logInfo(`conversationId: ${this.conversationId}, ani: ${this.ani}`);

    this.selectedBot = await this.botService.getBotIfExists(this.conversationId, this.ani);
    return this.selectedBot != null;
  }

  /*
   * Process the initial bot response
   */
  async processBotStart() {
    if (!this.selectedBot) {
      return;
    }

    try {
      logInfo('[Bot] Getting initial response');

      // Create a new request context for the initial greeting
      this.currentRequest = this.performanceLogger.createRequest();

      // Set a special user input for the initial greeting
      this.currentRequest.setUserInput('Initial Greeting');

      // Start the llmProcessing phase for the initial greeting
      const llmPhase = this.currentRequest.trackPhase('llmProcessing');

      let response;
      try {
        response = await this.selectedBot.getInitialResponse();

        // Store the AI reply for metrics
        this.currentAiReply = response.text || '';
        this.currentRequest.setAiReply(this.currentAiReply);
      } finally {
        // Always end the phase, even if an error occurs
        llmPhase.end();
      }

      // First send the turn response (text message)
      if (response.text) {
        logInfo('[Bot] Sending initial text response');

        // Start the textToSpeech phase
        const ttsPhase = this.currentRequest.trackPhase('textToSpeech');

        this.sendTurnResponse(response.disposition, response.text, response.confidence);

        // If there's no audio, end the TTS phase here
        if (!response.audioBytes) {
          ttsPhase.end();

          // Finalize the request
          this.currentRequest.finalize();
        }
        // If there is audio, the phase will be ended in sendAudio
      }

      // Then send the audio - PlaybackStarted/Completed events will manage isAudioPlaying
      if (response.audioBytes) {
        logInfo('[Bot] Sending initial audio response');
        await this.sendAudio(response.audioBytes);
      }
    } catch (error) {
      logError(
        `[Bot] Error in initial response: ${error instanceof Error ? error.message : String(error)}`
      );
      this.sendDisconnect('error', 'Failed to start conversation', {});
    }
  }

  private async initializeASRServiceOnce() {
    if (this.asrService) {
      return;
    }

    if (!this.conversationId) {
      throw new Error(
        '[Session] Cannot initialize ASR service before conversationId is set. Call setConversationId() first.'
      );
    }

    logInfo('[ASR] Initializing ASR service');

    try {
      this.asrService = await getASRService();
      this.asrService
        .on('error', (error: any) => {
          // End the speechToText phase on error
          this.performanceLogger.endPhase('speechToText');

          if (!this.dtmfManager.isCapturing()) {
            const message = 'Error during Speech Recognition.';
            logError(`${message}: ${error instanceof Error ? error.message : String(error)}`);
            this.sendDisconnect('error', message, {});
          }
        })
        // Handle interim transcripts
        .on('transcript', (transcript: Transcript) => {
          // Log interim transcripts for debugging
          if (transcript.text) {
            logInfo(`[ASR] Interim transcript: ${transcript.text}`);
          }
        })
        .on('final-transcript', async (transcript: Transcript) => {
          if (!this.dtmfManager.isCapturing()) {
            // Get the ASR streaming mode from environment
            const asrStreamingMode = process.env.ASR_STREAMING_MODE || 'standard';

            // In continuous mode, we NEVER process final transcripts from ASR
            // and rely SOLELY on our pause detection mechanism
            if (asrStreamingMode === 'continuous') {
              logInfo(
                `[ASR] Session IGNORING final transcript in continuous mode: "${transcript.text}"`
              );

              // DO NOTHING ELSE - don't process this transcript at all
              // The pause detection mechanism in AudioManager is the ONLY system
              // that should finalize transcripts in continuous mode
              return;
            }

            // For standard or hybrid mode, process final transcripts normally
            logInfo(`[ASR] Final transcript: ${transcript.text}`);

            // Create a new request context if we don't have one
            if (!this.currentRequest) {
              this.currentRequest = this.performanceLogger.createRequest();
            }

            // End the speechToText phase if it's active
            try {
              // Create a new phase context and immediately end it
              // This is safer than trying to track if the phase is already started
              const asrPhase = this.currentRequest.trackPhase('speechToText');
              asrPhase.end();
            } catch (err) {
              // Ignore errors - the phase might not be started
            }

            // Store the transcript text for metrics
            this.currentRequest.setUserInput(transcript.text);

            await this.processBotResponse(transcript);
          }
        });
    } catch (error) {
      logError(
        `[ASR] Failed to initialize ASR service: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
      this.sendDisconnect('error', 'Failed to initialize ASR service', {});
    }
  }

  private async processBotResponse(transcript: Transcript) {
    if (!this.selectedBot) {
      return;
    }

    // CRITICAL FIX: Add a flag to track if we're already processing a response
    // This prevents duplicate responses for the same input
    if (this.isProcessingResponse) {
      logInfo(
        `[BOT] Already processing a response, ignoring duplicate request for: "${transcript.text}"`
      );
      return;
    }

    // Simple duplicate detection to prevent processing the same input twice
    if (!this.justHadBargeIn) {
      const currentTime = Date.now();
      const timeSinceLastProcessed = currentTime - this.lastProcessedTimestamp;
      const isDuplicate =
        transcript.text === this.lastProcessedTranscript && timeSinceLastProcessed < 5000;

      if (isDuplicate) {
        logInfo(
          `[BOT] Ignoring duplicate transcript: "${transcript.text}" (processed ${timeSinceLastProcessed}ms ago)`
        );
        return;
      }

      // Update tracking for non-barge-in transcripts
      this.lastProcessedTranscript = transcript.text;
      this.lastProcessedTimestamp = currentTime;
    } else {
      // Reset the flag after using it
      this.justHadBargeIn = false;
      logBargeIn(`[BOT] Processing barge-in text: "${transcript.text}"`);
    }

    // Set the flag to indicate we're processing a response
    this.isProcessingResponse = true;

    if (!this.currentRequest) {
      // Create a new request context if we don't have one
      this.currentRequest = this.performanceLogger.createRequest();
      this.currentRequest.setUserInput(transcript.text);
    }

    try {
      // Start the LLM processing phase
      const llmPhase = this.currentRequest.trackPhase('llmProcessing');

      let response;
      try {
        response = await this.selectedBot.getBotResponse(transcript.text);

        // If response is null (e.g., due to barge-in cancellation), exit early
        if (!response) {
          logInfo('[BOT] Response processing was cancelled');
          return;
        }

        // Store the AI reply for metrics
        this.currentAiReply = response.text || '';
        this.currentRequest.setAiReply(this.currentAiReply);
      } finally {
        // Always end the phase, even if an error occurs
        llmPhase.end();
      }

      // At this point, we know response is not null

      // Process text response if available
      if (response.text) {
        logInfo(`[BOT] Sending text response: ${response.text}`);

        // Start the textToSpeech phase
        const ttsPhase = this.currentRequest.trackPhase('textToSpeech');

        this.sendTurnResponse(response.disposition, response.text, response.confidence);

        // If there's no audio, end the TTS phase here
        if (!response.audioBytes) {
          ttsPhase.end();

          // Finalize the request
          this.currentRequest.finalize();
          this.currentRequest = null;
        }
        // If there is audio, the phase will be ended in sendAudio
      }

      // Process audio response if available
      if (response.audioBytes) {
        logInfo('[Bot] Sending audio response');
        await this.sendAudio(response.audioBytes);
      }

      // Handle session end if needed
      logInfo(
        `[Bot] endSession: ${response.endSession} / endSessionResponse: ${response.endSessionReason}`
      );

      if (response.endSession) {
        this.sendDisconnect('completed', 'voicebot mission completed', {
          toAgent: response.endSessionReason === 'agent' ? 'true' : 'false',
        });
      }
    } catch (error) {
      logError(
        `[Bot] Error processing response: ${error instanceof Error ? error.message : String(error)}`
      );
      this.sendDisconnect('error', 'Failed to process bot response', {});
    } finally {
      // CRITICAL FIX: Always reset the processing flag, even if an error occurs
      // This ensures we don't get stuck in a state where we can't process new responses
      this.isProcessingResponse = false;
      logInfo(`[BOT] Finished processing response for: "${transcript.text}"`);
    }
  }

  async processBinaryMessage(data: Uint8Array) {
    if (this.disconnecting || this.closed || !this.selectedBot) {
      return;
    }

    if (this.dtmfManager.isCapturing()) {
      return;
    }

    try {
      if (!this.asrService) {
        await this.initializeASRServiceOnce();
      }

      // IMPORTANT: We no longer return early when audio is playing
      // This allows the AudioManager to detect speech during playback for barge-in
      // if (this.isAudioPlaying) {
      //   return;
      // }

      // If we don't have a current request, create one
      if (!this.currentRequest) {
        logInfo('[ASR] Starting new speech recognition request');
        this.currentRequest = this.performanceLogger.createRequest();
        // Start the speechToText phase
        this.currentRequest.trackPhase('speechToText');
      }

      // Process audio through existing ASR service
      // This will now work during audio playback, enabling barge-in detection
      if (this.asrService) {
        await this.asrService.processAudio(data);
      }
    } catch (error) {
      const message = 'Error processing audio data';
      logError(`${message}: ${error instanceof Error ? error.message : String(error)}`);
      this.sendDisconnect('error', message, {});
    }
  }

  processDTMF(digit: string) {
    if (this.disconnecting || this.closed || !this.selectedBot) {
      return;
    }

    // Process DTMF input regardless of audio playback state
    // This allows the DTMFManager to detect DTMF during playback for barge-in
    this.dtmfManager.processDigit(digit);
  }
}
