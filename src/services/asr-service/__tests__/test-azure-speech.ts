/* eslint-disable no-console */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { createSpeechService } from '../index';
import { Transcript } from '../types';

/**
 * Simple test script to verify Azure Speech integration
 *
 * To run:
 * 1. Ensure AZURE_SPEECH_KEY and AZURE_SPEECH_REGION are set in .env
 * 2. Run with: npx ts-node src/services/asr-service/__tests__/test-azure-speech.ts
 */

// Create mock PCMU (μ-law) audio data
const createMockAudioData = (durationMs = 1000): Uint8Array => {
  // 8000 samples per second * 1 byte per sample for PCMU
  const bytesPerSecond = 8000;
  const size = Math.floor((durationMs / 1000) * bytesPerSecond);
  const data = new Uint8Array(size);

  // Generate a simple sine wave encoded in PCMU
  for (let i = 0; i < size; i++) {
    // Generate sine wave between -32768 and 32767 (16-bit PCM range)
    const t = i / bytesPerSecond; // Time in seconds
    const frequency = 440; // 440 Hz tone
    const amplitude = 32767;
    const sample = Math.sin(2 * Math.PI * frequency * t) * amplitude;

    // Convert to PCMU (μ-law)
    // Basic μ-law encoding:
    // 1. Take the absolute value and add bias
    // 2. Convert to logarithmic scale
    // 3. Quantize to 8 bits
    const absValue = Math.abs(sample);
    const sign = sample < 0 ? 0x80 : 0;
    const biased = Math.min(absValue + 33, 32767);
    const lvalue = Math.floor((Math.log(biased) / Math.log(1 + 255)) * 255);
    data[i] = ~(sign | lvalue) & 0xff;
  }

  return data;
};

async function testSpeechRecognition() {
  console.log('Starting Azure Speech recognition test...');

  const speechService = createSpeechService();
  let receivedInterim = false;
  let receivedFinal = false;

  // Set up event handlers
  speechService
    .on('transcript', (transcript: Transcript) => {
      console.log('Received interim transcript:', transcript.text);
      console.log('Confidence:', transcript.confidence);
      receivedInterim = true;
    })
    .on('final-transcript', (transcript: Transcript) => {
      console.log('Received final transcript:', transcript.text);
      console.log('Confidence:', transcript.confidence);
      receivedFinal = true;
    })
    .on('error', (error: Error | string) => {
      console.error('Recognition error:', error);
    });

  try {
    // Send mock audio data
    console.log('Sending mock audio data...');
    const audioData = createMockAudioData(5000); // 5 seconds of audio
    speechService.processAudio(audioData);

    // Wait for results
    await new Promise<void>(resolve => {
      const checkResults = () => {
        if (receivedFinal) {
          resolve();
        } else {
          setTimeout(checkResults, 1000);
        }
      };
      setTimeout(checkResults, 1000);
    });

    console.log('Test completed successfully');
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    // Clean up
    speechService.dispose();
  }
}

// Run the test
testSpeechRecognition().catch(console.error);
