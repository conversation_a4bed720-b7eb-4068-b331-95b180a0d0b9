// @ts-ignore
// This require cannot be converted to an import due to SDK compatibility issues
const speechSDK = require('microsoft-cognitiveservices-speech-sdk');
import { BaseSpeechService } from './base-speech-service';
import { Transcript } from './types';

// μ-law to linear PCM conversion table
const MULAW_TO_PCM_TABLE = new Int16Array(256);
(() => {
  for (let i = 0; i < 256; i++) {
    const mu = i ^ 0xff; // Invert the bits
    const sign = mu & 0x80 ? -1 : 1;
    let magnitude = ((mu & 0x7f) << 1) | 1;
    magnitude = ((magnitude + 33) * 2049) >> 11;
    MULAW_TO_PCM_TABLE[i] = sign * magnitude;
  }
})();

/**
 * Converts μ-law encoded audio to 16-bit linear PCM
 * @param mulawData Input μ-law encoded audio data
 * @returns 16-bit linear PCM data
 */
function convertMulawToPcm(mulawData: Uint8Array): Int16Array {
  const pcmData = new Int16Array(mulawData.length);
  for (let i = 0; i < mulawData.length; i++) {
    pcmData[i] = MULAW_TO_PCM_TABLE[mulawData[i]];
  }
  return pcmData;
}

// Validation function removed as it was unused

/**
 * Implementation of speech recognition service using Azure Cognitive Services
 */
export class AzureSpeechService extends BaseSpeechService {
  private recognizer: any = null;
  private audioStream: any = null;
  private config: any = null;
  private initialized = false;
  private initPromise: Promise<void>;

  constructor() {
    super();
    this.initPromise = this.initializeSpeechService().catch(error => {
      this.emitError(
        error instanceof Error ? error : new Error('Failed to initialize Azure Speech Service')
      );
      throw error;
    });
  }

  private async initializeSpeechService(): Promise<void> {
    try {
      console.log('Initializing Azure Speech Service');
      const key = process.env.AZURE_SPEECH_KEY;
      const region = process.env.AZURE_SPEECH_REGION;

      if (!key || !region) {
        throw new Error(
          'Missing required Azure Speech configuration. Check AZURE_SPEECH_KEY and AZURE_SPEECH_REGION environment variables.'
        );
      }

      //await validateAzureSpeechKey(key, region);

      this.config = speechSDK.SpeechConfig.fromSubscription(key, region);
      this.config.speechRecognitionLanguage = 'cs-CZ';

      this.config.setProperty('SpeechServiceConnection_SpeakerAudioLowQuality', 'true');
      this.config.setProperty('SpeechServiceConnection_MaxRetryTimeMs', '5000');
      this.config.setProperty('SpeechServiceConnection_EnableAudioPreprocessing', 'true');
      this.config.setProperty('SpeechServiceConnection_EnableBandwidthOptimization', 'true');
      this.config.setProperty('SpeechServiceConnection_InitialSilenceTimeoutMs', '10000');
      this.config.setProperty('SpeechServiceConnection_EndSilenceTimeoutMs', '5000');

      // this.config.setProperty(
      //   speechSDK.PropertyId.SpeechServiceConnection_EndSilenceTimeoutMs,
      //   "5000"
      // );
      // this.config.setProperty(
      //   speechSDK.PropertyId.SpeechServiceConnection_InitialSilenceTimeoutMs,
      //   "5000"
      // );
      // this.config.setProperty(
      //   speechSDK.PropertyId
      //     .SpeechServiceResponse_RequestDetailedResultTrueFalse,
      //   "true"
      // );
      // this.config.setProfanity(speechSDK.ProfanityOption.Raw);
      // this.config.setProperty(
      //   speechSDK.PropertyId.SpeechServiceConnection_SingleLanguageIdPriority,
      //   "high"
      // );
      // this.config.setProperty(
      //   speechSDK.PropertyId.Speech_SegmentationSilenceTimeoutMs,
      //   "1000"
      // );
      // this.config.setProperty(
      //   speechSDK.PropertyId.SpeechServiceConnection_RecognitionEnergyThreshold,
      //   "0"
      // );
      // this.config.speechSynthesisOutputFormat =
      //   speechSDK.SpeechSynthesisOutputFormat.Raw8Khz8BitMonoMULaw;
      // this.config.setProperty(
      //   speechSDK.PropertyId.SpeechServiceConnection_OriginalSampleRateInHz,
      //   "8000"
      // );

      // Configure for 8KHz PCMU input that we'll convert to PCM
      const audioFormat = speechSDK.AudioStreamFormat.getWaveFormatPCM(
        8000, // sample rate
        16, // bits per sample (after μ-law conversion)
        1 // channels
      );
      this.audioStream = speechSDK.AudioInputStream.createPushStream(audioFormat);
      const audioConfig = speechSDK.AudioConfig.fromStreamInput(this.audioStream);
      this.recognizer = new speechSDK.SpeechRecognizer(this.config, audioConfig);

      this.recognizer.startContinuousRecognitionAsync(
        () => {
          this.state = 'Processing';
        },
        (err: any) => {
          throw err;
        }
      );

      await this.setupRecognizerCallbacks();
      this.initialized = true;
    } catch (error) {
      this.initialized = false;
      this.emitError(
        error instanceof Error ? error : new Error('Failed to initialize Azure Speech Service')
      );
      throw error;
    }
  }

  private async setupRecognizerCallbacks(): Promise<void> {
    if (!this.recognizer) {
      throw new Error('Recognizer not initialized');
    }

    return new Promise((resolve, reject) => {
      this.recognizer.recognizing = (_: any, event: any) => {
        if (event.result.text) {
          const transcript = {
            text: event.result.text,
            confidence: 0.5,
          };
          console.log('[Customer] ' + transcript.text + ' (interim)'); // Keep customer speech
          this.emitTranscript(transcript);
        }
      };

      this.recognizer.recognized = (_: any, event: any) => {
        switch (event.result.reason) {
          case speechSDK.ResultReason.RecognizedSpeech:
            if (event.result.text) {
              const transcript = {
                text: event.result.text,
                confidence: event.result.confidence || 0.9,
              };
              console.log('[Customer] ' + transcript.text); // Keep customer speech
              this.emitTranscript(transcript, true);
            }
            break;

          case speechSDK.ResultReason.NoMatch:
            console.log('[Error] Speech not recognized'); // Keep critical error
            const noMatchDetail = speechSDK.NoMatchDetails.fromResult(event.result);
            if (noMatchDetail.reason === speechSDK.NoMatchReason.NotRecognized) {
              console.log('[Error] Speech not recognized clearly'); // Keep critical error
            }
            break;
        }
      };

      this.recognizer.canceled = (_: any, event: any) => {
        if (event.reason === speechSDK.CancellationReason.Error) {
          console.log('[Error] Speech recognition error:', event.errorDetails); // Keep critical error
          switch (event.errorCode) {
            case speechSDK.CancellationErrorCode.ServiceTimeout:
              console.log('[Error] Service timeout'); // Keep critical error
              this.restartRecognition().catch(() => {
                this.emitError('Failed to restart recognition after timeout');
              });
              break;
            case speechSDK.CancellationErrorCode.ServiceError:
              console.log('[Error] Service error - check network and credentials'); // Keep critical error
              this.emitError('Service error - please check network connection');
              break;
            case speechSDK.CancellationErrorCode.RuntimeError:
              console.log('[Error] Runtime error - audio format may be incompatible'); // Keep critical error
              this.emitError('Runtime error - audio format may be incompatible');
              break;
            default:
              console.log('[Error] Unknown error:', event.errorDetails); // Keep critical error
              this.emitError(`Unknown error: ${event.errorDetails}`);
          }
        }
      };

      if (this.audioStream.on) {
        this.audioStream.on('error', (error: any) => {
          console.log('[Error] Audio stream error:', error); // Keep critical error
          this.emitError(`Audio stream error: ${error}`);
          this.restartRecognition().catch(() => {
            console.log('[Error] Failed to restart recognition'); // Keep critical error
          });
        });
      }

      this.recognizer.startContinuousRecognitionAsync(
        () => {
          console.log('Speech recognition Processing');
          this.state = 'Processing';
          resolve();
        },
        (error: any) => {
          this.emitError(error);
          reject(error);
        }
      );
    });
  }

  private restartRecognition = async (): Promise<void> => {
    try {
      if (this.recognizer) {
        await new Promise<void>((resolve, reject) => {
          this.recognizer.stopContinuousRecognitionAsync(
            () => {
              this.recognizer?.startContinuousRecognitionAsync(
                () => resolve(),
                (error: any) => reject(error)
              );
            },
            (error: any) => reject(error)
          );
        });
      }
    } catch (error) {
      throw error;
    }
  };

  async processAudio(data: Uint8Array): Promise<void> {
    try {
      await this.initPromise;

      if (!this.initialized) {
        throw new Error('Speech service initialization failed');
      }

      if (this.state === 'Complete') {
        this.emitError('Speech recognition has already completed');
        return;
      }

      if (!this.audioStream || !this.recognizer) {
        throw new Error('Speech service not properly initialized');
      }

      const pcmData = convertMulawToPcm(data);
      this.audioStream.write(pcmData.buffer);
      this.state = 'Processing';
    } catch (error) {
      this.emitError(error instanceof Error ? error : new Error('Failed to process audio data'));
    }
  }

  dispose(): void {
    try {
      if (this.recognizer) {
        this.recognizer.stopContinuousRecognitionAsync(
          () => {
            this.recognizer?.close();
            this.recognizer = null;
          },
          (error: any) => {
            console.log('[Error] Failed to stop recognition:', error); // Keep critical error
          }
        );
      }

      if (this.audioStream) {
        this.audioStream.close();
        this.audioStream = null;
      }

      if (this.config) {
        this.config = null;
      }

      this.initialized = false;
      this.state = 'Complete';
    } catch (error) {
      console.log('[Error] Failed to dispose speech service:', error); // Keep critical error
    }
  }

  // Track the last transcript
  private lastTranscript: Transcript | null = null;

  /**
   * Override the emitTranscript method to track the last transcript
   */
  protected emitTranscript(transcript: Transcript, isFinal = false): void {
    // Store the transcript if it's final
    if (isFinal) {
      this.lastTranscript = transcript;
    }
    // Call the parent method to emit the event
    super.emitTranscript(transcript, isFinal);
  }

  /**
   * Get the last final transcript
   * @returns The last transcript or null if none exists
   */
  getLastTranscript(): Transcript | null {
    return this.lastTranscript;
  }
}
