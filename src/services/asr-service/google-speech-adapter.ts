import { BaseSpeechService } from './base-speech-service';
// SpeechServiceState is imported but not used directly - keeping for future use
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { SpeechServiceState } from './types';
import { GoogleSpeechService } from '../speech/google/google-speech-service';
import { logInfo, logError, logDebug } from '../logging/logging';
// Import the speech service types from the Google Speech service
import { Transcript as GoogleTranscript } from '../speech/base/types';

/**
 * Adapter class that adapts GoogleSpeechService to the BaseSpeechService interface
 * used by the ASR service.
 */
export class GoogleSpeechAdapter extends BaseSpeechService {
  private googleService: GoogleSpeechService;
  protected isIgnoringAudioInput = false;

  constructor() {
    super();
    this.googleService = new GoogleSpeechService();
    this.setupEventHandlers();
  }

  /**
   * Initialize the Google Speech service
   */
  async initialize(): Promise<void> {
    try {
      await this.googleService.initialize();
      // Log streaming mode and config at stream setup
      const { getASRStreamingMode } = require('../../common/environment-variables');
      const asrStreamingMode = getASRStreamingMode();
      logDebug(
        `[ASR] Google Speech Adapter stream setup: mode=${asrStreamingMode}, config=unavailable`
      );
      // Use the correct state type from the ASR service
      this.state = 'Processing';
      logInfo('[ASR] Google Speech Adapter initialized successfully');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logError(`[ASR] Failed to initialize Google Speech Adapter: ${errorMessage}`);
      this.emitError(errorMessage);
      this.state = 'Error';
      throw error;
    }
  }

  /**
   * Set up event handlers to forward events from GoogleSpeechService to this adapter
   */
  private setupEventHandlers(): void {
    // Import the ASR streaming mode configuration
    const { getASRStreamingMode } = require('../../common/environment-variables');

    // Forward transcript events - use the correct type
    this.googleService.on('transcript', (googleTranscript: GoogleTranscript) => {
      logDebug(
        `[ASR] [GoogleSpeechAdapter] Received transcript event (interim): raw=${JSON.stringify(
          googleTranscript
        )}, text="${googleTranscript.text}", confidence=${googleTranscript.confidence ?? 0.5}`
      );
      // Create the transcript object
      const transcript = {
        text: googleTranscript.text,
        // Ensure confidence is always a number (default to 0.5 if undefined)
        confidence: googleTranscript.confidence ?? 0.5,
      };

      // Always emit the transcript event, even in continuous mode
      // The AudioManager will use this to update its internal state for pause detection
      this.emitTranscript(transcript, false);
    });

    // Forward final transcript events - use the correct type
    this.googleService.on('final-transcript', (googleTranscript: GoogleTranscript) => {
      // Get the current ASR streaming mode
      const asrStreamingMode = getASRStreamingMode();

      logDebug(
        `[ASR] [GoogleSpeechAdapter] Received final-transcript event: raw=${JSON.stringify(
          googleTranscript
        )}, text="${googleTranscript.text}", confidence=${
          googleTranscript.confidence ?? 0.9
        }, mode=${asrStreamingMode}`
      );

      // Create the transcript object
      const transcript = {
        text: googleTranscript.text,
        // Ensure confidence is always a number (default to 0.9 if undefined)
        confidence: googleTranscript.confidence ?? 0.9,
      };

      // In continuous mode, we still want to emit the final transcript as an interim transcript
      // This ensures the AudioManager has the text to work with for pause detection
      if (asrStreamingMode === 'continuous') {
        logInfo(
          `[ASR] Found final result: "${transcript.text}" with confidence ${transcript.confidence}`
        );
        // Emit as interim transcript to trigger pause detection in AudioManager
        this.emitTranscript(transcript, false);
      } else {
        // For non-continuous modes, emit as a final transcript
        this.emitTranscript(transcript, true);
      }
    });

    // Forward error events
    this.googleService.on('error', (error: Error | string) => {
      this.emitError(error);
    });
  }

  /**
   * Process audio data through the Google Speech service
   */
  async processAudio(data: Uint8Array): Promise<void> {
    try {
      // Forward the isIgnoringAudioInput flag to the Google service
      if (this.isIgnoringAudioInput) {
        this.googleService.startIgnoringAudioInput();
      } else {
        this.googleService.stopIgnoringAudioInput();
      }

      // Process the audio data
      await this.googleService.processAudio(data);
      this.state = 'Processing';
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logError(`[ASR] Error processing audio in Google Speech Adapter: ${errorMessage}`);
      this.emitError(errorMessage);
      throw error;
    }
  }

  /**
   * Start ignoring audio input (e.g. during TTS playback)
   */
  startIgnoringAudioInput(): void {
    this.isIgnoringAudioInput = true;
    this.googleService.startIgnoringAudioInput();
    logDebug('[ASR] Google Speech Adapter: Started ignoring audio input');
  }

  /**
   * Stop ignoring audio input and resume processing
   */
  stopIgnoringAudioInput(): void {
    this.isIgnoringAudioInput = false;
    this.googleService.stopIgnoringAudioInput();
    logDebug('[ASR] Google Speech Adapter: Stopped ignoring audio input');
  }

  /**
   * Clean up resources used by the speech service
   */
  dispose(): void {
    try {
      this.googleService.dispose();
      this.state = 'None';
      this.emitter.removeAllListeners();
      logDebug('[ASR] Google Speech Adapter disposed successfully');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logError(`[ASR] Error disposing Google Speech Adapter: ${errorMessage}`);
      throw new Error(errorMessage);
    }
  }
}
