import { Transcript } from '../speech';
import { BaseSpeechService } from '../asr-service/base-speech-service';
import { isBargeInEnabled, getPauseThresholdMs } from '../../common/environment-variables';
import { logDebug, logError } from '../logging/logging';
import { BargeInManager } from '../barge-in/barge-in-manager';
import type { EventDrivenStateManager } from '../../session/state-machine/event-driven-state-manager';

/**
 * AudioManager: Manages audio processing, speech recognition, barge-in detection, and pause detection.
 * Reconstructed from scratch due to file loss. Some logic may need to be re-implemented as needed.
 */
export class AudioManager {
  private asrService: BaseSpeechService | null = null;
  private _bargeInEnabled = isBargeInEnabled();
  private stateManager: EventDrivenStateManager;
  private _bargeInManager: BargeInManager;
  private pauseDetectionTimer: NodeJS.Timeout | null = null;
  private currentText = '';
  private _lastFinalizedText = '';
  private lastFinalizedTimestamp = 0;
  private isFinalizingTranscript = false;
  private PAUSE_THRESHOLD_MS: number = getPauseThresholdMs();
  private onTranscript: (transcript: Transcript, isFinal: boolean) => void;
  private _onBargeIn: (source: 'speech', text: string) => void;
  private _onError: (error: Error | string) => void;

  constructor(
    onTranscript: (transcript: Transcript, isFinal: boolean) => void,
    onBargeIn: (source: 'speech', text: string) => void,
    onError: (error: Error | string) => void,
    stateManager: EventDrivenStateManager,
    bargeInManager: BargeInManager
  ) {
    this.onTranscript = onTranscript;
    this._onBargeIn = onBargeIn;
    this._onError = onError;
    this.stateManager = stateManager;
    this._bargeInManager = bargeInManager;
  }

  /**
   * Processes audio data through the ASR service.
   */
  async processAudio(_data: Uint8Array): Promise<void> {
    if (!this.asrService) {
      // TODO: Initialize ASR service as needed
      logError('[AudioManager] ASR service not initialized');
      return;
    }
    // TODO: Implement actual audio processing and transcript update logic
    // For now, just log
    logDebug('[AudioManager] processAudio called');
  }

  /**
   * Starts the pause detection timer for stable stream processing.
   */
  public startPauseDetection(): void {
    if (this.pauseDetectionTimer) {
      clearInterval(this.pauseDetectionTimer);
      this.pauseDetectionTimer = null;
    }
    this.pauseDetectionTimer = setInterval(() => {
      // TODO: Implement pause detection logic
      // For demonstration, simulate a finalized transcript if currentText is non-empty and enough time has passed
      const now = Date.now();
      if (
        this.currentText &&
        now - this.lastFinalizedTimestamp > this.PAUSE_THRESHOLD_MS &&
        !this.isFinalizingTranscript
      ) {
        this.isFinalizingTranscript = true;
        const finalTranscript: Transcript = {
          text: this.currentText,
          // No confidence provided - better than inventing one
        };
        const textToFinalize = this.currentText;
        this.currentText = '';
        this._lastFinalizedText = textToFinalize;
        this.lastFinalizedTimestamp = now;

        try {
          logDebug(`[AudioManager] Sending finalized transcript to bot: "${finalTranscript.text}"`);
          if (
            typeof this.stateManager.isStateTransitionInProgress === 'function' &&
            this.stateManager.isStateTransitionInProgress()
          ) {
            this.stateManager.requestInterruption({
              nextState: 'PROCESSING_INPUT',
              transcript: finalTranscript,
              inputType: 'speech',
              reason: 'Pause detection finalized transcript',
            });
          } else {
            this.onTranscript(finalTranscript, true);
          }
        } catch (err) {
          logError(
            `[AudioManager] Error calling onTranscript with final transcript: ${
              err instanceof Error ? err.message : String(err)
            }`
          );
        } finally {
          this.isFinalizingTranscript = false;
        }
      }
    }, 100);
  }

  /**
   * Stops the pause detection timer if active.
   */
  public stopPauseDetection(): void {
    if (this.pauseDetectionTimer) {
      clearInterval(this.pauseDetectionTimer);
      this.pauseDetectionTimer = null;
      logDebug('[AudioManager] Stopped pause detection timer');
    }
  }

  // TODO: Implement additional methods for barge-in, DTMF, ASR service management, etc.

  /**
   * Initializes the AudioManager. (Stub implementation)
   */
  public async initialize(): Promise<void> {
    // TODO: Implement actual initialization logic if needed
    logDebug('[AudioManager] initialize() called (stub)');
  }
}
