/**
 * AudioServiceAdapter
 *
 * Adapts the Session class to the AudioManager and related audio services.
 * This adapter encapsulates all audio-related functionality that was previously
 * embedded in the Session class, providing a clean interface for audio handling.
 *
 * Now supports event emission for event-driven architecture.
 */

// AudioManager import removed as it's not used
import { BargeInManager } from '../barge-in/barge-in-manager';
import { EnhancedASRService } from '../asr-service/index';
import { SessionState } from '../../session/session-state-manager';
import { EventDrivenStateManager } from '../../session/state-machine/event-driven-state-manager';
import { IWebSocketController } from '../../session/websocket-controller';
import { RequestContext } from '../monitoring';
import { logInfo, logWarning, logError, logDebug } from '../logging/logging';
import { Transcript } from '../speech';
import { DTMFManager } from '../dtmf/dtmf-manager';
import { EventEmitter, EventPriority } from '../events/event-emitter';
import {
  SessionEventType,
  AudioDataEventData,
  PlaybackStartedEventData,
  PlaybackCompletedEventData,
  TranscriptReceivedEventData,
} from '../events/session-events';

export type TranscriptHandler = (transcript: Transcript, isFinal: boolean) => void;
export type ErrorHandler = (error: Error | string) => void;

/**
 * Options for AudioServiceAdapter
 */
export interface AudioServiceAdapterOptions {
  /**
   * Optional event emitter for event-based communication
   */
  eventEmitter?: EventEmitter;
}

export class AudioServiceAdapter {
  // AudioManager removed: all audio/ASR/barge-in logic is now handled by adapters and managers.
  private bargeInManager: BargeInManager;
  private enhancedASRService: EnhancedASRService | null = null;
  private stateManager: EventDrivenStateManager;
  private wsController: IWebSocketController;
  private dtmfManager: DTMFManager | null = null;
  private eventEmitter?: EventEmitter;
  private isEventEmissionEnabled = true;

  // Track the current ASR phase context for the utterance
  private currentAsrPhaseContext: ReturnType<RequestContext['trackPhase']> | null = null;
  private currentAsrMetricsContext: RequestContext | null = null;

  // Track the current TTS phase context for the request
  private currentTtsPhaseContext: ReturnType<RequestContext['trackPhase']> | null = null;

  constructor(
    bargeInManager: BargeInManager,
    wsController: IWebSocketController,
    stateManager: EventDrivenStateManager,
    dtmfManager?: DTMFManager,
    options?: AudioServiceAdapterOptions
  ) {
    this.bargeInManager = bargeInManager;
    this.wsController = wsController;
    this.stateManager = stateManager;
    this.dtmfManager = dtmfManager || null;
    this.eventEmitter = options?.eventEmitter;

    // If we have an event emitter, log that event emission is enabled
    if (this.eventEmitter) {
      logInfo('[AudioServiceAdapter] Event emission enabled');
    }
  }

  /**
   * Set the event emitter after construction
   * This allows adding event emission to an existing AudioServiceAdapter
   */
  public setEventEmitter(eventEmitter: EventEmitter): void {
    this.eventEmitter = eventEmitter;
    logInfo('[AudioServiceAdapter] Event emitter set, event emission enabled');
  }

  /**
   * Enable or disable event emission
   */
  public setEventEmissionEnabled(enabled: boolean): void {
    this.isEventEmissionEnabled = enabled;
    logInfo(`[AudioServiceAdapter] Event emission ${enabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * Emit an event if event emission is enabled
   */
  private emit<T>(
    eventType: SessionEventType,
    data: T,
    priority: EventPriority = EventPriority.MEDIUM
  ): void {
    if (this.eventEmitter && this.isEventEmissionEnabled) {
      this.eventEmitter.emit(eventType, data, priority);
    }
  }

  /**
   * Set the ASR service and connect it to the AudioManager
   */
  setASRService(asrService: EnhancedASRService): void {
    this.enhancedASRService = asrService;

    // Connect the ASR service to the AudioManager for pause detection
    if (asrService) {
      // Get the underlying BaseSpeechService from the EnhancedASRService
      const baseSpeechService = asrService.getASRService();

      if (baseSpeechService) {
        // Set the ASR service for pause detection if needed (AudioManager removed)
        // TODO: Wire baseSpeechService to pause detection logic or adapter as needed.
        logDebug('[AudioServiceAdapter] Connected ASR service for pause detection');

        // Subscribe to transcript events if we have an event emitter
        if (this.eventEmitter) {
          // The EnhancedASRService doesn't expose a direct onTranscript method,
          // but we can use the constructor callbacks that are already set up
          // We'll hook into the ASR service events directly
          baseSpeechService
            .on('transcript', (transcript: Transcript) => {
              // Emit transcript received event for interim transcripts
              // Use HIGH priority for interim transcripts to ensure they're processed quickly for barge-in
              this.emit(
                SessionEventType.TRANSCRIPT_RECEIVED,
                {
                  transcript,
                  isFinal: false,
                  timestamp: Date.now(),
                } as TranscriptReceivedEventData,
                EventPriority.HIGH // Changed to HIGH priority for faster barge-in detection
              );
            })
            .on('final-transcript', (transcript: Transcript) => {
              // Emit transcript received event for final transcripts
              this.emit(
                SessionEventType.TRANSCRIPT_RECEIVED,
                {
                  transcript,
                  isFinal: true,
                  timestamp: Date.now(),
                } as TranscriptReceivedEventData,
                EventPriority.HIGH
              );
              // End the ASR phase timing for the current utterance
              if (this.currentAsrPhaseContext) {
                this.currentAsrPhaseContext.end();
                this.currentAsrPhaseContext = null;
                this.currentAsrMetricsContext = null;
              }
            });

          logInfo('[AudioServiceAdapter] Subscribed to ASR transcript events');
        }
      } else {
        logError('[AudioServiceAdapter] Failed to get BaseSpeechService from EnhancedASRService');
      }
    }
  }

  /**
   * Process audio data through the ASR service
   */
  async processAudio(data: Uint8Array, metricsContext?: RequestContext): Promise<void> {
    // Check if we're in a valid state to process audio
    if (this.stateManager.isDisconnectingOrClosed()) {
      return;
    }

    // If we don't have an ASR service, we can't process audio
    if (!this.enhancedASRService) {
      logWarning('[AudioServiceAdapter] No ASR service available, cannot process audio');
      return;
    }

    // Emit audio data event
    this.emit(
      SessionEventType.AUDIO_DATA,
      {
        audioData: data,
        timestamp: Date.now(),
      } as AudioDataEventData,
      EventPriority.LOW // Lower priority since this is a high-frequency event
    );

    // Start the ASR phase only if not already started for this utterance
    if (metricsContext && !this.currentAsrPhaseContext) {
      this.currentAsrPhaseContext = metricsContext.trackPhase('speechToText');
      this.currentAsrMetricsContext = metricsContext;
    }

    const currentState = this.stateManager.getState();

    // Handle state transitions based on current state
    if (currentState === SessionState.IDLE || currentState === SessionState.PLAYING) {
      // These states can directly transition to PROCESSING_INPUT
      // State transition to PROCESSING_INPUT is now handled only after a transcript is available.
      // The Session class (handleTranscript) will perform the transition with the required metadata.
    } else if (
      currentState === SessionState.PROCESSING_BOT ||
      currentState === SessionState.RESPONDING
    ) {
      // For these states, we still process audio but don't change state
      // This allows barge-in detection to work while respecting the state machine
      // The barge-in system will handle the state transition when appropriate
      if (this.bargeInManager.isBargeInEnabled() && this.stateManager.isPlaying()) {
        // If barge-in is enabled and we're in PLAYING state, barge-in can occur
        logInfo('[AudioServiceAdapter] Processing audio for potential barge-in detection');
      } else {
        // Otherwise, we're just collecting audio but not changing state yet
        // logDebug('[AudioServiceAdapter] Collecting audio while in ' + currentState + ' state');
      }
    }

    // Always process the audio data regardless of state
    // This ensures barge-in detection works and audio isn't lost
    await this.enhancedASRService.processAudio(data);
  }

  /**
   * Send audio data to the client
   */
  async sendAudio(bytes: Uint8Array, metricsContext?: RequestContext): Promise<void> {
    // Check if session is closed
    if (this.stateManager.isDisconnectingOrClosed()) {
      logWarning(
        '[AudioServiceAdapter] Attempted to send audio after session was closed or disconnecting.'
      );
      return;
    }

    // Only transition to PLAYING state if we're not already in RESPONDING state
    // If we're in RESPONDING, the StartPlaybackAction will handle the transition
    const currentState = this.stateManager.getState();
    if (currentState !== SessionState.RESPONDING) {
      // Check if a transition is already in progress
      if (
        typeof this.stateManager.isStateTransitionInProgress === 'function' &&
        this.stateManager.isStateTransitionInProgress()
      ) {
        // Get information about the current transition
        const currentTransition =
          typeof this.stateManager.getCurrentTransitionInfo === 'function'
            ? this.stateManager.getCurrentTransitionInfo()
            : null;

        if (currentTransition) {
          logInfo(
            `[AudioServiceAdapter] Not transitioning to PLAYING: another transition is already in progress (${currentTransition.from} → ${currentTransition.to})`
          );
        } else {
          logInfo(
            '[AudioServiceAdapter] Not transitioning to PLAYING: a transition is in progress'
          );
        }
      } else {
        // No transition in progress, so we can transition to PLAYING
        logInfo('[AudioServiceAdapter] Transitioning to PLAYING state from ' + currentState);
        await this.stateManager.setState(SessionState.PLAYING, {
          reason: 'Sending audio to client',
          audioSize: bytes.length,
        });
      }
    } else {
      logInfo(
        '[AudioServiceAdapter] Already in RESPONDING state, StartPlaybackAction will handle transition to PLAYING'
      );
    }

    // Enable barge-in detection in BargeInManager
    this.bargeInManager.enableBargeIn();

    // Store the TTS phase context so it can be ended on playback completed
    if (metricsContext) {
      this.currentTtsPhaseContext = metricsContext.trackPhase('textToSpeech');
    }

    try {
      logDebug(`[Audio] Sending ${bytes.length} bytes of TTS audio data`);

      // Emit playback started event
      this.emit(
        SessionEventType.PLAYBACK_STARTED,
        {
          audioBytes: bytes,
          timestamp: Date.now(),
        } as PlaybackStartedEventData,
        EventPriority.MEDIUM
      );

      // Use the WebSocketController to send audio (handles chunking internally)
      await this.wsController.sendAudio(bytes);

      logDebug(
        `[Audio] TTS audio sent (${bytes.length} bytes) - waiting for PlaybackStarted event`
      );
    } catch (error) {
      // If an error occurs, disable barge-in detection
      this.bargeInManager.disableBargeIn();

      // Transition back to IDLE state
      await this.stateManager.setState(SessionState.IDLE, {
        reason: 'Error sending audio',
      });

      // Re-throw the error to be handled by the caller
      throw error;
    } finally {
      // Do not end the TTS phase here; it will be ended on playback completed event
    }
  }

  /**
   * Set the playback state
   */
  async setPlaybackState(state: 'playing' | 'paused' | 'stopped'): Promise<void> {
    // Enable or disable barge-in based on playback state
    if (state === 'playing') {
      this.bargeInManager.enableBargeIn();

      // Emit playback started event
      this.emit(
        SessionEventType.PLAYBACK_STARTED,
        {
          timestamp: Date.now(),
        } as PlaybackStartedEventData,
        EventPriority.MEDIUM
      );
    } else if (state === 'stopped') {
      this.bargeInManager.disableBargeIn();

      // End the TTS phase timing for the current request, if any
      if (this.currentTtsPhaseContext) {
        this.currentTtsPhaseContext.end();
        this.currentTtsPhaseContext = null;
      }

      // Emit playback completed event
      this.emit(
        SessionEventType.PLAYBACK_COMPLETED,
        {
          timestamp: Date.now(),
        } as PlaybackCompletedEventData,
        EventPriority.MEDIUM
      );
    }

    // Update session state based on playback state
    if (state === 'playing' && !this.stateManager.isPlaying()) {
      // Check if a transition is already in progress
      if (
        typeof this.stateManager.isStateTransitionInProgress === 'function' &&
        this.stateManager.isStateTransitionInProgress()
      ) {
        // Get information about the current transition
        const currentTransition =
          typeof this.stateManager.getCurrentTransitionInfo === 'function'
            ? this.stateManager.getCurrentTransitionInfo()
            : null;

        if (currentTransition) {
          logInfo(
            `[AudioServiceAdapter] Not transitioning to PLAYING: another transition is already in progress (${currentTransition.from} → ${currentTransition.to})`
          );
          return;
        }
      }

      await this.stateManager.setState(SessionState.PLAYING, {
        reason: 'Audio playback started',
        playbackStatus: state,
      });
    } else if (state === 'stopped' && this.stateManager.isPlaying()) {
      // Check if a transition is already in progress
      if (
        typeof this.stateManager.isStateTransitionInProgress === 'function' &&
        this.stateManager.isStateTransitionInProgress()
      ) {
        // Get information about the current transition
        const currentTransition =
          typeof this.stateManager.getCurrentTransitionInfo === 'function'
            ? this.stateManager.getCurrentTransitionInfo()
            : null;

        if (currentTransition) {
          logInfo(
            `[AudioServiceAdapter] Not transitioning to IDLE: another transition is already in progress (${currentTransition.from} → ${currentTransition.to})`
          );
          return;
        }
      }

      await this.stateManager.setState(SessionState.IDLE, {
        reason: 'Audio playback stopped',
        playbackStatus: state,
      });
    }
  }

  /**
   * Get the current playback state based on session state
   */
  getPlaybackState(): 'playing' | 'paused' | 'stopped' {
    // Determine playback state based on session state
    if (this.stateManager.isPlaying()) {
      return 'playing';
    } else {
      return 'stopped';
    }
  }

  /**
   * Process DTMF input
   * @param digit The DTMF digit to process
   */
  processDTMF(digit: string): void {
    // Check if we have a DTMF manager
    if (this.dtmfManager) {
      // Process DTMF input through the DTMFManager
      this.dtmfManager.processDigit(digit);
      logInfo(`[AudioServiceAdapter] Processed DTMF digit: ${digit}`);
    } else {
      logWarning('[AudioServiceAdapter] No DTMFManager available, cannot process DTMF');
    }
  }

  /**
   * Set the DTMF manager
   * @param dtmfManager The DTMF manager to use
   */
  setDTMFManager(dtmfManager: DTMFManager): void {
    this.dtmfManager = dtmfManager;
  }

  /**
   * Dispose of audio resources
   */
  async dispose(): Promise<void> {
    // Dispose of ASR service
    if (this.enhancedASRService) {
      try {
        await this.enhancedASRService.dispose();
      } catch (error) {
        logError(
          `[AudioServiceAdapter] Error disposing ASR service: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }

    // AudioManager removed: no disposal needed.
    // (No AudioManager disposal needed.)
  }
  /**
   * Arm the ASR/audio pipeline to be ready for new user input.
   * This should be called when entering the IDLE state.
   */
  public async armForInput(): Promise<void> {
    // Always (re)start pause detection timer when preparing for user input
    // AudioManager removed: pause detection should be handled by ASR service or another component if needed.

    const sessionState = this.stateManager.getState?.() || 'unknown';
    const asrType = this.enhancedASRService?.constructor?.name || typeof this.enhancedASRService;
    logDebug(
      `[AudioServiceAdapter] armForInput: Called in session state: ${sessionState}, ASR type: ${asrType}`
    );

    if (this.stateManager.isDisconnectingOrClosed()) {
      logInfo('[AudioServiceAdapter] Not arming ASR for input: session is disconnecting or closed');
      return;
    }
    if (!this.enhancedASRService) {
      logWarning('[AudioServiceAdapter] Cannot arm ASR for input: no ASR service set');
      return;
    }
    try {
      logDebug('[AudioServiceAdapter] armForInput: Calling enhancedASRService.armForInput()');
      await this.enhancedASRService.armForInput();
      logInfo('[AudioServiceAdapter] ASR/audio pipeline armed for new user input');
    } catch (error) {
      logError(
        `[AudioServiceAdapter] Error arming ASR for input: ${
          error instanceof Error ? error.stack || error.message : String(error)
        }`
      );
    }
    logDebug('[AudioServiceAdapter] armForInput: Completed');
  }
}
