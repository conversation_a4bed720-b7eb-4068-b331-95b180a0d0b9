# Audio Service: Technical Overview

## Purpose

The Audio Service manages audio processing, playback, and integration with speech recognition (ASR) for the application. It handles audio input/output streams, coordinates with barge-in detection, and provides a clean interface for session integration through the AudioServiceAdapter.

---

## Key Responsibilities

- Process audio input from clients
- Manage audio playback and state
- Integrate with ASR for speech recognition
- <PERSON>ect pauses in speech for transcript finalization
- Support barge-in detection during audio playback
- Provide a clean adapter interface for session integration

---

## Core Components and Their Roles

### AudioManager (`src/services/audio/audio-manager.ts`)

- Manages audio input processing and ASR integration
- Implements pause detection for stable stream processing
- Detects speech during playback for barge-in
- Emits transcript and barge-in events

### AudioServiceAdapter (`src/services/audio/audio-service-adapter.ts`)

- Adapts the Session class to audio-related functionality
- Encapsulates audio processing, playback, and DTMF handling
- Manages state transitions related to audio
- Provides a clean interface for session integration

---

## Pause Detection

The AudioManager implements pause detection to finalize transcripts when natural pauses in speech occur:

- Tracks the timestamp of the last detected speech in interim transcripts
- If a pause longer than the configured threshold is detected, emits a final transcript
- The pause threshold can be configured using the `PAUSE_THRESHOLD_MS` environment variable
- Required for both Hybrid and Continuous ASR streaming modes

This enables the system to process user utterances as soon as a natural pause is detected, rather than waiting for the ASR's built-in silence detection.

---

## Component Interactions

1. **Initialization**: AudioManager is created and initialized
2. **ASR Integration**: ASR service is set on the AudioManager
3. **Audio Processing**: Audio data is processed and sent to ASR
4. **Pause Detection**: Natural pauses in speech are detected
5. **Transcript Emission**: Transcripts are emitted based on pause detection or ASR finals
6. **Barge-In Detection**: Speech during playback is detected for barge-in
7. **Audio Playback**: Audio data is sent to the client through WebSocket

---

## Configuration Options

The Audio Service can be configured through environment variables:

- `ENABLE_BARGE_IN`: Enable barge-in functionality (true/false)
- `BARGE_IN_CONFIDENCE_THRESHOLD`: Confidence threshold for speech barge-in
- `PAUSE_THRESHOLD_MS`: Pause threshold for transcript finalization (default: 1500ms)
- `ENABLE_STABLE_STREAM`: Enable pause detection (true/false)
- `ASR_STREAMING_MODE`: ASR streaming mode ('standard', 'hybrid', or 'continuous')

---

## Design Principles

- **Separation of Concerns**: Clear separation between audio processing and session logic
- **Adapter Pattern**: Clean interface for session integration
- **Event-Driven Architecture**: Asynchronous event emission for transcripts and barge-in
- **Configurable Behavior**: Support for different streaming modes and settings
- **Error Handling**: Consistent error reporting and recovery

---

## File Structure

- `src/services/audio/`
  - `index.ts` – Exports AudioManager and AudioServiceAdapter
  - `audio-manager.ts` – Core audio processing and ASR integration
  - `audio-service-adapter.ts` – Adapter for session integration

---

## Usage Example

```typescript
// Create AudioManager with handlers
const audioManager = new AudioManager(
  (transcript, isFinal) => {
    // Handle transcript
    console.log(`Transcript ${isFinal ? '(final)' : '(interim)'}: ${transcript.text}`);
  },
  (source, text) => {
    // Handle barge-in
    console.log(`Barge-in detected from ${source} with text: ${text}`);
  },
  error => {
    // Handle error
    console.error('Audio error:', error);
  },
  bargeInManager
);

// Initialize AudioManager
await audioManager.initialize();

// Create AudioServiceAdapter
const audioAdapter = new AudioServiceAdapter(
  audioManager,
  bargeInManager,
  wsController,
  stateManager,
  dtmfManager
);

// Set ASR service on adapter
audioAdapter.setASRService(asrService);

// Process audio data
await audioAdapter.processAudio(audioData, metricsContext);

// Send audio to client
await audioAdapter.sendAudio(audioBytes, metricsContext);
```

---

## Integration with Other Services

The Audio Service integrates with several other services:

- **ASR Service**: For speech recognition and transcript generation
- **Barge-In Manager**: For barge-in detection and playback state management
- **DTMF Manager**: For DTMF input processing
- **WebSocket Controller**: For sending audio data to clients
- **Session State Manager**: For state transitions related to audio
