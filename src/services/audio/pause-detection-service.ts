import { Transcript } from '../speech';
import { logDebug } from '../monitoring/logging';

/**
 * PauseDetectionService: Generic service for detecting pauses in speech and finalizing transcripts.
 * Can be composed into any ASR pipeline or session flow.
 */
export class PauseDetectionService {
  private onFinalTranscript: (transcript: Transcript) => void;
  private pauseThresholdMs: number;
  private pauseTimer: NodeJS.Timeout | null = null;
  private lastText = '';
  private lastSpeechTimestamp = 0;

  constructor(onFinalTranscript: (transcript: Transcript) => void, pauseThresholdMs = 1200) {
    this.onFinalTranscript = onFinalTranscript;
    this.pauseThresholdMs = pauseThresholdMs;
  }

  /**
   * Call this method for every transcript event (interim or final).
   */
  public handleTranscript(transcript: Transcript, isFinal: boolean) {
    // Only log at debug level when needed
    if (isFinal) {
      this.clearPauseTimer();
      this.onFinalTranscript(transcript);
      return;
    }
    // For interim transcripts, start/reset the pause timer
    this.lastText = transcript.text;
    this.lastSpeechTimestamp = Date.now();
    this.resetPauseTimer();
  }

  private resetPauseTimer() {
    this.clearPauseTimer();
    this.pauseTimer = setTimeout(() => {
      if (this.lastText) {
        // Log at debug level for pause detection
        logDebug(`[PauseDetectionService] Pause detected, finalizing transcript`);
        this.onFinalTranscript({
          text: this.lastText,
          // No confidence provided - better than inventing one
        });
        this.lastText = '';
      }
    }, this.pauseThresholdMs);
  }

  private clearPauseTimer() {
    if (this.pauseTimer) {
      clearTimeout(this.pauseTimer);
      this.pauseTimer = null;
    }
  }

  public dispose() {
    this.clearPauseTimer();
  }
}
