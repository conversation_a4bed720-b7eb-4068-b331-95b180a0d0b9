import * as fs from 'fs';
import * as path from 'path';
import { InMemoryDatabaseService } from '../in-memory-database-service';
import { BaseTTSService } from '../speech';
import { getTTSService } from '../tts-service';
import { BotResponse } from './bot-response';
import { ChatMessage, LLMProvider, LLMProviderOptions } from './types';
import { LLMProviderFactory, LLMProviderType } from './providers';
import { logInfo, logError, logWarning, shouldLogOpenAIRequests } from '../monitoring/logging';

/**
 * This class provides support for the various methods needed to interact with a Bot.
 * It uses an LLM provider for generating responses and maintains conversation context.
 */
export class BotResource {
  private ttsService!: BaseTTSService;
  private conversationHistory: ChatMessage[] = [];
  private ani: string | null = null;
  private conversationId: string | null = null;
  private dbService: InMemoryDatabaseService;
  private llmProvider!: LLMProvider;
  private turnCancelled = false;

  // Request deduplication mechanism
  private lastUserInput = '';
  private lastUserInputTimestamp = 0;
  private readonly DUPLICATE_REQUEST_WINDOW_MS: number = 8000; // 8 seconds
  private constructor(dbService: InMemoryDatabaseService) {
    this.dbService = dbService;
  }

  /**
   * Cancels the current turn when a barge-in is detected
   *
   * Sets the turnCancelled flag to true, causing in-progress LLM requests
   * to be ignored when they complete. This prevents partial responses from
   * being processed after the user has interrupted the bot.
   *
   * Critical for barge-in functionality to:
   * 1. Stop background processing of interrupted responses
   * 2. Keep the system responsive to new input
   * 3. Prevent conflicting responses
   */
  public cancelCurrentTurn(): void {
    logInfo('[BOT] Cancelling current turn due to barge-in');
    this.turnCancelled = true;
  }

  /**
   * Create a new BotResource instance
   * @param ani The ANI (caller ID)
   * @param conversationId The conversation ID
   * @param dbService The database service
   * @returns A new BotResource instance
   */
  static async create(
    ani: string,
    conversationId: string,
    dbService: InMemoryDatabaseService
  ): Promise<BotResource> {
    const instance = new BotResource(dbService);
    try {
      instance.ani = ani;
      instance.conversationId = conversationId;

      // Initialize TTS service
      instance.ttsService = await getTTSService();

      // Initialize LLM provider
      const providerType = (process.env.LLM_PROVIDER_TYPE || 'azure') as LLMProviderType;
      instance.llmProvider = LLMProviderFactory.getProvider(providerType);
      await instance.llmProvider.initialize();

      const ttsProvider =
        process.env.TTS_PROVIDER?.toLowerCase() || process.env.SPEECH_SERVICE?.toLowerCase();

      let promptFile: string;
      switch (ttsProvider) {
        case 'google':
          promptFile = 'system-prompt-google.txt';
          break;
        case 'azure':
          promptFile = 'system-prompt-azure.txt';
          break;
        case 'elevenlabs':
          promptFile = 'system-prompt-elevenlabs.txt';
          break;
        default:
          logWarning('[BOT] Unknown TTS provider, defaulting to Google prompt');
          promptFile = 'system-prompt-google.txt';
      }

      let systemPrompt = fs.readFileSync(path.join(process.cwd(), promptFile), 'utf8');
      systemPrompt = `ConversationId: ${conversationId}\nAni: ${ani}\n---\n${systemPrompt}`;

      if (process.env.CUSTOM_SYSTEM_PROMPT) {
        systemPrompt = process.env.CUSTOM_SYSTEM_PROMPT;
      }

      // Get last conversation summary if available
      if (instance.ani) {
        const lastSummary = await instance.dbService.getLastSummary(instance.ani);
        if (lastSummary) {
          const formattedDate = new Intl.DateTimeFormat('cs-CZ', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
          }).format(lastSummary.endTime);

          const summaryContext = `\n---\nZákazník tě naposledy kontaktovat dne ${formattedDate} a hovořili jste o ${lastSummary.summaryText}`;
          systemPrompt += summaryContext;
        }
      }

      // Log a truncated version of the system prompt
      if (shouldLogOpenAIRequests) {
        const firstLine = systemPrompt.split('\n')[0] || '';
        logInfo(`[BOT] Using system prompt: ${firstLine}... (${systemPrompt.length} chars)`);
      }

      instance.conversationHistory.push({
        role: 'system',
        content: systemPrompt,
      });

      const historyFromPreviousChatInstance = await instance.dbService.getConversationHistory(
        ani,
        conversationId
      );

      if (historyFromPreviousChatInstance && historyFromPreviousChatInstance.length > 0) {
        instance.conversationHistory = [
          ...instance.conversationHistory,
          ...historyFromPreviousChatInstance.map(message => ({
            role: message.isBot ? ('assistant' as const) : ('user' as const),
            content: message.messageText,
          })),
        ];
      }

      logInfo('[BOT] Bot resource created successfully');
      return instance;
    } catch (error) {
      logError(
        `[BOT] Failed to create bot resource: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      );
      throw error;
    }
  }

  /**
   * Get a response from the LLM
   * @param messages The conversation history
   * @param abortSignal Optional AbortSignal to cancel the request
   * @returns The LLM's response text
   */
  private async getLLMResponse(
    messages: ChatMessage[],
    abortSignal?: AbortSignal
  ): Promise<string> {
    try {
      // Check if the request has been aborted
      if (abortSignal && abortSignal.aborted) {
        logInfo('[BOT] LLM request aborted before sending');
        throw new Error('Request aborted');
      }

      // Only log if OpenAI requests logging is enabled
      if (shouldLogOpenAIRequests) {
        logInfo(`[BOT] LLM Request - Conversation ID: ${this.conversationId}, ANI: ${this.ani}`);
      }

      const options: LLMProviderOptions = {
        temperature: 0.7,
        maxTokens: 800,
        topP: 0.95,
        frequencyPenalty: 0,
        presencePenalty: 0,
        stop: null,
        abortSignal: abortSignal,
      };

      const response = await this.llmProvider.getChatCompletion(messages, options);

      // Check again if the request has been aborted
      if (abortSignal && abortSignal.aborted) {
        logInfo('[BOT] LLM request aborted after receiving response');
        throw new Error('Request aborted');
      }

      return response;
    } catch (error) {
      // If the error is due to an aborted request, handle it gracefully
      if (abortSignal && abortSignal.aborted) {
        logInfo('[BOT] LLM request aborted');
        throw new Error('Request aborted');
      }

      logError(
        `Error getting LLM response: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
      throw error;
    }
  }

  /**
   * Get the initial response from the bot
   * @param abortSignal Optional AbortSignal to cancel the request
   * @returns A BotResponse object
   */
  async getInitialResponse(abortSignal?: AbortSignal): Promise<BotResponse> {
    try {
      // Check if the request has been aborted
      if (abortSignal && abortSignal.aborted) {
        logInfo('[BOT] Initial response request aborted before sending');
        return this.getErrorResponse();
      }

      const response = await this.getLLMResponse(this.conversationHistory, abortSignal);
      logInfo(`[BOT] Initial response received (${response.length} chars)`);

      // Check if the request has been aborted
      if (abortSignal && abortSignal.aborted) {
        logInfo('[BOT] Initial response request aborted after receiving LLM response');
        return this.getErrorResponse();
      }

      this.conversationHistory.push({
        role: 'assistant',
        content: response,
      });

      const audioBytes = await this.ttsService.getAudioBytes(response);

      // Check if the request has been aborted
      if (abortSignal && abortSignal.aborted) {
        logInfo('[BOT] Initial response request aborted after generating audio');
        return this.getErrorResponse();
      }

      logInfo(`[BOT] Initial response audio generated (${audioBytes.length} bytes)`);
      return new BotResponse('match', response).withConfidence(1.0).withAudioBytes(audioBytes);
    } catch (error) {
      // If the error is due to an aborted request, handle it gracefully
      if (abortSignal && abortSignal.aborted) {
        logInfo('[BOT] Initial response request aborted');
        return this.getErrorResponse();
      }

      logError(
        `Error in getInitialResponse: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
      return this.getErrorResponse();
    }
  }

  /**
   * Get a response from the bot based on user input
   *
   * Processes user input and generates a response with barge-in cancellation support:
   * - Resets turnCancelled flag at the start of each turn
   * - Checks the flag at key points during execution
   * - Returns null if cancelled, allowing Session to handle the interruption
   *
   * @param data The user input text
   * @returns A BotResponse object, or null if cancelled by barge-in
   */
  async getBotResponse(data: string, abortSignal?: AbortSignal): Promise<BotResponse | null> {
    this.turnCancelled = false; // Reset cancellation for new turn
    try {
      if (this.turnCancelled || (abortSignal && abortSignal.aborted)) {
        return null;
      }

      // Check for duplicate requests
      const currentTime = Date.now();
      const normalizedInput = data.trim().toLowerCase();
      const normalizedLastInput = this.lastUserInput.trim().toLowerCase();
      const timeSinceLastRequest = currentTime - this.lastUserInputTimestamp;

      // If this is a duplicate request within the window, log and return the last response
      if (
        normalizedInput === normalizedLastInput &&
        timeSinceLastRequest < this.DUPLICATE_REQUEST_WINDOW_MS
      ) {
        logInfo(
          `[BOT] Detected duplicate request: "${data}" (last request: "${this.lastUserInput}" ${
            timeSinceLastRequest / 1000
          }s ago)`
        );

        // Get the last bot response from conversation history
        const lastBotResponse = this.getLastBotResponse();
        if (lastBotResponse) {
          logInfo(`[BOT] Returning cached response for duplicate request`);
          // Generate audio for the cached response
          const audioBytes = await this.ttsService.getAudioBytes(lastBotResponse);
          if (abortSignal && abortSignal.aborted) {
            return null;
          }
          return new BotResponse('match', lastBotResponse)
            .withConfidence(1.0)
            .withAudioBytes(audioBytes);
        }
      }

      // Update the last user input tracking
      this.lastUserInput = data;
      this.lastUserInputTimestamp = currentTime;

      logInfo(`[BOT] Received user input: ${data}`);

      if (this.ani && this.conversationId) {
        await this.dbService.storeMessage({
          ani: this.ani,
          conversationId: this.conversationId,
          messageText: data,
          isBot: false,
        });
        if (this.turnCancelled) {
          return null;
        }
      }

      this.conversationHistory.push({
        role: 'user',
        content: data,
      });
      if (this.turnCancelled) {
        return null;
      }

      const response = await this.getLLMResponse(this.conversationHistory, abortSignal);
      if (this.turnCancelled || (abortSignal && abortSignal.aborted)) {
        return null;
      }
      logInfo(`[BOT] AI response received (${response.length} chars)`);

      this.conversationHistory.push({
        role: 'assistant',
        content: response,
      });
      if (this.turnCancelled) {
        return null;
      }

      if (this.ani && this.conversationId) {
        await this.dbService.storeMessage({
          ani: this.ani,
          conversationId: this.conversationId,
          messageText: response,
          isBot: true,
        });
        if (this.turnCancelled) {
          return null;
        }
      }

      const escalateReason = this.shouldEscalateToAgent(response);
      const shouldEndSession = escalateReason !== undefined;

      const textToTts = this.cleanBotResponse(response);
      const audioBytes = await this.ttsService.getAudioBytes(textToTts);
      if (this.turnCancelled) {
        return null;
      }
      return new BotResponse('match', textToTts)
        .withConfidence(1.0)
        .withAudioBytes(audioBytes)
        .withEndSession(shouldEndSession, escalateReason);
    } catch (error) {
      console.error('Error in getBotResponse:', error);
      return this.getErrorResponse();
    }
  }

  /**
   * Clean the bot response by removing special tokens
   * @param text The text to clean
   * @returns The cleaned text
   */
  private cleanBotResponse(text: string): string {
    const stringsToRemove = ['[[END]]', '[[AGENT]]'];
    let cleanedText = text;

    for (const str of stringsToRemove) {
      const escapedStr = str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      cleanedText = cleanedText.replace(new RegExp(escapedStr, 'g'), '');
    }

    return cleanedText.trim();
  }

  /**
   * Check if the session should end
   * @param response The bot response
   * @returns Whether the session should end
   */
  private shouldEndSession(response: string): boolean {
    const endPhrases = ['[[END]]'];
    return endPhrases.some(phrase => response.toLowerCase().includes(phrase.toLowerCase()));
  }

  /**
   * Check if the conversation should be escalated to an agent
   * @param response The bot response
   * @returns The reason for escalation, if any
   */
  private shouldEscalateToAgent(response: string): 'agent' | 'end' | undefined {
    const endPhrases = ['[[AGENT]]'];
    const toAgent = endPhrases.some(phrase =>
      response.toLowerCase().includes(phrase.toLowerCase())
    );
    return toAgent ? 'agent' : this.shouldEndSession(response) ? 'end' : undefined;
  }

  /**
   * Get an error response
   * @returns A BotResponse object for an error
   */
  private getErrorResponse(): BotResponse {
    logWarning('[BOT] Returning error response to user');
    const errorMessage =
      'Omlouvám se, ale momentálně mám potíže se zpracováním vašeho požadavku. Prosím, zkuste to znovu později.';
    return new BotResponse('no_match', errorMessage).withConfidence(0).withEndSession(true);
  }

  /**
   * Get a response from the LLM for conversation summary
   * @param messages The messages to summarize
   * @returns The summary text
   */
  async getResponseForSummary(messages: ChatMessage[]): Promise<string> {
    return this.getLLMResponse(messages);
  }

  /**
   * Get the last bot response from the conversation history
   * @returns The last bot response, or null if none exists
   */
  private getLastBotResponse(): string | null {
    // Find the last assistant message in the conversation history
    for (let i = this.conversationHistory.length - 1; i >= 0; i--) {
      const message = this.conversationHistory[i];
      if (message.role === 'assistant' && message.content) {
        return this.cleanBotResponse(message.content);
      }
    }
    return null;
  }

  /**
   * Clean up resources used by the bot
   */
  async dispose(): Promise<void> {
    try {
      // Clean up resources
      if (this.llmProvider) {
        await this.llmProvider.dispose();
      }
    } catch (error) {
      logError(
        `[BOT] Error in dispose: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    } finally {
      this.conversationHistory = [];
      this.ani = null;
      this.conversationId = null;
    }
  }
}
