import { InMemoryDatabaseService } from '../in-memory-database-service';
import { BotResource } from './bot-resource';
import { BotResponse } from './bot-response';
import { RequestContext } from '../monitoring';

/**
 * Interface for the result of processing user input with a bot.
 */
export interface ProcessedBotResponse {
  success: boolean;
  response?: BotResponse;
  shouldEndSession?: boolean;
  endSessionReason?: string;
}

/**
 * This class provides support for retrieving a Bot Resource based on the supplied
 * connection URL and input variables.
 */
export class BotService {
  readonly dbService: InMemoryDatabaseService;
  private botInstances: Map<string, BotResource>;

  constructor() {
    console.log('[BOT] Initializing BotService');
    this.dbService = InMemoryDatabaseService.getInstance();
    this.botInstances = new Map();
  }

  /**
   * Process user input with the given bot resource and track metrics.
   * @param botResource The bot resource to use for processing
   * @param userInput The user's input string
   * @param metricsContext The request context for metrics tracking
   * @returns A ProcessedBotResponse object
   */
  async processUserInput(
    botResource: BotResource,
    userInput: string,
    metricsContext: RequestContext,
    abortSignal?: AbortSignal
  ): Promise<ProcessedBotResponse> {
    const llmPhase = metricsContext.trackPhase('llmProcessing');
    try {
      const response = await botResource.getBotResponse(userInput, abortSignal);

      if (!response) {
        return { success: false };
      }

      if (response.text) {
        metricsContext.setAiReply(response.text);
      }

      return {
        success: true,
        response,
        shouldEndSession: response.endSession,
        endSessionReason: response.endSessionReason,
      };
    } finally {
      llmPhase.end();
    }
  }

  /**
   * Get a bot instance for the specified conversation, creating one if it doesn't exist
   * @param conversationId The conversation ID
   * @param ani The ANI (caller ID)
   * @returns A BotResource instance, or null if the inputs are invalid
   */
  async getBotIfExists(
    conversationId: string | undefined,
    ani: string | undefined
  ): Promise<BotResource | null> {
    if (!ani || !conversationId) {
      console.error('[BOT] Missing ANI or conversationId in input variables');
      return null;
    }

    // Check if we already have a bot instance for this conversation
    const existingBot = this.botInstances.get(conversationId);
    if (existingBot) {
      return existingBot;
    }

    // Create new bot instance if none exists
    const newBot = await BotResource.create(ani, conversationId, this.dbService);
    this.botInstances.set(conversationId, newBot);
    return newBot;
  }

  /**
   * Delete a bot instance for the specified conversation
   * @param conversationId The conversation ID
   */
  async deleteBotService(conversationId: string): Promise<void> {
    console.log('[BOT] Deleting bot instance for conversation', conversationId);
    const bot = this.botInstances.get(conversationId);
    if (bot) {
      await bot.dispose();
      this.botInstances.delete(conversationId);
      console.log(`[BOT] Deleted bot instance for conversation ${conversationId}`);
    }
  }
}
