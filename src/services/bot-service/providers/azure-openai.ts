import { LLMProvider, <PERSON>MProviderOptions, ChatMessage } from '../types';
import { createExternalHttpClient } from '../../../utils/http-client';
import axios from 'axios';
import {
  logInfo,
  logError,
  logOpenAIRequest,
  logOpenAIResponse,
  shouldLogOpenAIRequests,
  shouldLogOpenAIResponses,
} from '../../../services/monitoring/logging';

/**
 * Azure OpenAI implementation of the LLM provider interface
 */
export class AzureOpenAIProvider implements LLMProvider {
  private apiKey: string | null = null;
  private endpoint: string | null = null;
  private deploymentName: string | null = null;

  /**
   * Initialize the Azure OpenAI provider with configuration from environment variables
   */
  async initialize(): Promise<void> {
    try {
      const key = process.env.AZURE_OPENAI_KEY;
      const endpoint = process.env.AZURE_OPENAI_ENDPOINT;
      const deploymentName = process.env.AZURE_OPENAI_DEPLOYMENT_NAME;

      if (!key || !endpoint || !deploymentName) {
        throw new Error(
          'Missing required Azure OpenAI configuration. Check AZURE_OPENAI_KEY, AZURE_OPENAI_ENDPOINT, and AZURE_OPENAI_DEPLOYMENT_NAME environment variables.'
        );
      }

      this.apiKey = key;
      this.endpoint = endpoint;
      this.deploymentName = deploymentName;

      logInfo('[LLM] Azure OpenAI provider initialized');
    } catch (error) {
      logError(
        `Failed to initialize Azure OpenAI provider: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      );
      throw error;
    }
  }

  /**
   * Get a chat completion from Azure OpenAI
   * @param messages The conversation history
   * @param options Options for the completion request
   * @returns The LLM's response text
   */
  async getChatCompletion(messages: ChatMessage[], options?: LLMProviderOptions): Promise<string> {
    if (!this.apiKey || !this.endpoint || !this.deploymentName) {
      throw new Error('Azure OpenAI provider not properly initialized');
    }

    // Check if the request has been aborted
    if (options?.abortSignal && options.abortSignal.aborted) {
      logInfo('[LLM] Azure OpenAI request aborted before sending');
      throw new Error('Request aborted');
    }

    try {
      const url = `${this.endpoint}/openai/deployments/${this.deploymentName}/chat/completions?api-version=2023-05-15`;

      // Create the request payload
      const requestPayload = {
        messages,
        temperature: options?.temperature ?? 0.7,
        max_tokens: options?.maxTokens ?? 800,
        top_p: options?.topP ?? 0.95,
        frequency_penalty: options?.frequencyPenalty ?? 0,
        presence_penalty: options?.presencePenalty ?? 0,
        stop: options?.stop ?? null,
      };

      // Log request details if enabled
      if (shouldLogOpenAIRequests) {
        logOpenAIRequest(`[LLM] Azure OpenAI Request: ${this.deploymentName}`);

        // Extract just the user messages for a more concise log
        const userMessages = messages
          .filter(msg => msg.role === 'user')
          .map(msg => msg.content)
          .filter(Boolean);

        logInfo(`[LLM] User messages: ${userMessages.join(' | ')}`);
      }

      // Create a client specifically for this external API call
      const client = createExternalHttpClient(url, {
        headers: {
          'Content-Type': 'application/json',
          'api-key': this.apiKey,
        },
      });

      // Log the request URL if enabled
      if (shouldLogOpenAIRequests) {
        logInfo(`[LLM] Making Azure OpenAI request to ${url}`);
      }

      // Create axios request config with abort signal
      const requestConfig: any = {};
      if (options?.abortSignal) {
        requestConfig.signal = options.abortSignal;
      }

      // Check again if the request has been aborted
      if (options?.abortSignal && options.abortSignal.aborted) {
        logInfo('[LLM] Azure OpenAI request aborted before sending');
        throw new Error('Request aborted');
      }

      // Pass the full URL to be explicit
      const response = await client.post(url, requestPayload, requestConfig);

      // Check if the request has been aborted after receiving response
      if (options?.abortSignal && options.abortSignal.aborted) {
        logInfo('[LLM] Azure OpenAI request aborted after receiving response');
        throw new Error('Request aborted');
      }

      const result = response.data;
      const responseMessage = result.choices?.[0]?.message;

      if (!responseMessage?.content) {
        throw new Error('No response from Azure OpenAI');
      }

      // Log the response if enabled
      if (shouldLogOpenAIResponses) {
        logOpenAIResponse(`[LLM] Azure OpenAI response: ${responseMessage.content}`);
      }

      return responseMessage.content;
    } catch (error) {
      // If the error is due to an aborted request, handle it gracefully
      if (options?.abortSignal && options.abortSignal.aborted) {
        logInfo('[LLM] Azure OpenAI request aborted');
        throw new Error('Request aborted');
      }

      if (axios.isAxiosError(error) && error.response) {
        logError(
          `Azure OpenAI Error Response: Status ${error.response.status} - ${error.response.statusText}`
        );
        // Log more details at debug level if needed
      }
      logError(
        `Error getting Azure OpenAI response: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      );
      throw error;
    }
  }

  /**
   * Clean up resources used by the provider
   */
  async dispose(): Promise<void> {
    this.apiKey = null;
    this.endpoint = null;
    this.deploymentName = null;
    logInfo('[LLM] Azure OpenAI provider disposed');
  }
}
