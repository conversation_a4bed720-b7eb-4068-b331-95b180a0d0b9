import { DTMFService } from './dtmf-service';
import { isBargeInEnabled } from '../../common/environment-variables';
import { logInfo } from '../monitoring/logging';

/**
 * Manages DTMF digit processing, sequence completion, and barge-in detection
 */
export class DTMFManager {
  private dtmfService: DTMFService | null = null;
  private isCapturingDTMF = false;
  private bargeInManager: import('../barge-in/index').BargeInManager;
  private bargeInEnabled = isBargeInEnabled();

  /**
   * Creates a new DTMFManager
   * @param onDigitsComplete Callback for when a DTMF sequence is completed
   * @param onBargeIn Callback for when a DTMF barge-in is detected
   */
  constructor(
    private onDigitsComplete: (digits: string) => void,
    private onBargeIn: (source: 'dtmf', digit: string) => void,
    bargeInManager: import('../barge-in/index').BargeInManager
  ) {
    this.bargeInManager = bargeInManager;
  }

  /**
   * Process a DTMF digit
   * @param digit The DTMF digit to process
   */
  public processDigit(digit: string): void {
    // Detect DTMF barge-in if barge-in is enabled
    if (this.bargeInEnabled && this.bargeInManager.isBargeInEnabled()) {
      logInfo(`[DTMF] Barge-in detected with digit: ${digit}`);
      this.onBargeIn('dtmf', digit);
    }

    // Handle DTMF processing
    if (!this.isCapturingDTMF) {
      this.isCapturingDTMF = true;
    }
    if (!this.dtmfService || this.dtmfService.getState() === 'Complete') {
      this.setupDTMFService();
    }
    this.dtmfService?.processDigit(digit);
  }

  /**
   * Sets up a new DTMFService instance with event handlers
   * @private
   */
  private setupDTMFService(): void {
    this.dtmfService = new DTMFService();
    this.dtmfService.on('final-digits', (digits: string) => {
      logInfo(`[DTMF] Sequence complete: ${digits}`);
      this.onDigitsComplete(digits);
      this.isCapturingDTMF = false;
    });
  }

  /**
   * Updates the audio playing state
   * @param isPlaying Whether audio is currently playing
   */
  // setAudioPlayingState is now managed by BargeInManager

  /**
   * Checks if DTMF capture is currently active
   * @returns True if DTMF capture is active, false otherwise
   */
  public isCapturing(): boolean {
    return this.isCapturingDTMF;
  }
}
