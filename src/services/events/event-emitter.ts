/**
 * EventEmitter: Core event system for the event-driven architecture.
 *
 * This class provides a type-safe event subscription and emission mechanism,
 * with support for event priorities and error handling.
 *
 * @module services/events/event-emitter
 */

import { logError } from '../monitoring/logging';

/**
 * Event priority levels
 */
export enum EventPriority {
  HIGH = 0,
  MEDIUM = 1,
  LOW = 2,
}

/**
 * Event listener function type
 */
export type EventListener<T = any> = (data: T) => void | Promise<void>;

/**
 * Event with priority and data
 */
interface PrioritizedEvent<T = any> {
  eventName: string;
  priority: EventPriority;
  data: T;
}

/**
 * Subscription object returned when subscribing to an event
 */
export interface Subscription {
  unsubscribe: () => void;
}

/**
 * EventEmitter class for event-driven communication between components
 */
export class EventEmitter {
  private listeners: Map<string, Array<EventListener>> = new Map();
  private eventQueue: PrioritizedEvent[] = [];
  private isProcessing = false;

  /**
   * Subscribe to an event
   *
   * @param eventName The name of the event to subscribe to
   * @param listener The listener function to call when the event is emitted
   * @returns A subscription object with an unsubscribe method
   */
  public on<T = any>(eventName: string, listener: EventListener<T>): Subscription {
    if (!this.listeners.has(eventName)) {
      this.listeners.set(eventName, []);
    }

    const eventListeners = this.listeners.get(eventName)!;
    eventListeners.push(listener as EventListener);

    return {
      unsubscribe: () => {
        const index = eventListeners.indexOf(listener as EventListener);
        if (index !== -1) {
          eventListeners.splice(index, 1);
        }
      },
    };
  }

  /**
   * Emit an event with the given name and data
   *
   * @param eventName The name of the event to emit
   * @param data The data to pass to the event listeners
   * @param priority The priority of the event (default: MEDIUM)
   */
  public emit<T = any>(
    eventName: string,
    data: T,
    priority: EventPriority = EventPriority.MEDIUM
  ): void {
    // Add the event to the queue
    this.eventQueue.push({
      eventName,
      priority,
      data,
    });

    // Sort the queue by priority (lower number = higher priority)
    this.eventQueue.sort((a, b) => a.priority - b.priority);

    // Process the queue if not already processing
    if (!this.isProcessing) {
      this.processEventQueue();
    }
  }

  /**
   * Process the event queue
   */
  private async processEventQueue(): Promise<void> {
    if (this.eventQueue.length === 0) {
      this.isProcessing = false;
      return;
    }

    this.isProcessing = true;

    // Get the next event from the queue
    const event = this.eventQueue.shift()!;
    const { eventName, data } = event;

    // Get the listeners for this event
    const eventListeners = this.listeners.get(eventName) || [];

    // Call each listener
    for (const listener of eventListeners) {
      try {
        const result = listener(data);
        if (result instanceof Promise) {
          await result;
        }
      } catch (error) {
        logError(
          `Error in event listener for ${eventName}: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }

    // Process the next event in the queue
    setTimeout(() => this.processEventQueue(), 0);
  }

  /**
   * Remove all listeners for a specific event
   *
   * @param eventName The name of the event to remove listeners for
   */
  public removeAllListeners(eventName: string): void {
    this.listeners.delete(eventName);
  }

  /**
   * Clear all event listeners
   */
  public clearAllListeners(): void {
    this.listeners.clear();
  }
}
