# Unified Logging System Guide

This document provides a comprehensive guide to the application's unified logging system.

## Architecture Overview

The logging system has been consolidated to provide a single, consistent implementation:

- **Core Implementation**: Located in `src/services/monitoring/logging.ts`
- **Compatibility Layer**: Files in `src/services/logging` re-export from the core implementation

For new code, import from the standard path:

```typescript
import { logInfo, logDebug, logError } from '../services/logging';
```

## Key Features

- **Color-coded logs** with timestamps for better readability
- **Hierarchical log levels** (DEBUG, INFO, METRICS, WARN, ERROR)
- **Flexible environment variable control** via `LOG_LEVEL` or `METRICS_LOG_LEVEL`
- **Category-specific logging** for HTTP, OpenAI, WebSocket, and ASR
- **Formatting utilities** for consistent log presentation

## Log Levels

The system supports both string-based and numeric log levels:

### String-based (via `LOG_LEVEL`)

- `DEBUG` - All logs including detailed debugging information
- `INFO` - Informational logs and above
- `METRICS` - Only metrics logs and above (default)
- `WARN` - Only warnings and errors
- `ERROR` - Only errors

### Numeric (via `METRICS_LOG_LEVEL`)

- `0`: DEBUG
- `1`: INFO
- `2`: METRICS (default)
- `3`: WARN
- `4`: ERROR

## Category-Specific Logging

Enable or disable specific log categories using environment variables:

```
LOG_HTTP_REQUESTS=true
LOG_HTTP_RESPONSES=true
LOG_OPENAI_REQUESTS=true
LOG_OPENAI_RESPONSES=true
LOG_WEBSOCKET_MESSAGES=true
LOG_ASR_INTERIM=true
```

These specialized logging functions only output logs when their corresponding environment variable is set to `true`:

```typescript
logHttpRequest('Sending request to API');
logOpenAIRequest('Sending request to OpenAI');
logWebSocketMessage('Received message from client');
logASRInterim('Interim transcript: Hello');
```

## Helper Functions

The module provides helper functions for formatting log messages:

- `truncateString(str, maxLength)` - Truncates a string if it's too long
- `formatSystemPrompt(prompt)` - Formats a system prompt for logging (truncated)

## Logging of Session and Interaction Statistics

The logging system is used to record session and interaction statistics from the application's monitoring and metrics subsystem. All key metrics—such as per-interaction timings and conversation summaries—are logged using `logMetrics` at the METRICS log level.

### Example: Session and Interaction Statistics

```typescript
// These logs are automatically generated by the monitoring system:
logMetrics(
  '[INTERACTION] ConvID: 123, ReqID: 456, User: "hello", ASR: 120ms, LLM: 300ms, TTS: 80ms, Total: 500ms'
);
logMetrics('[CONVERSATION SUMMARY] ConvID: 123, Interactions: 5');
```

These logs are emitted after each user interaction and when a session is completed, providing a clear record of system performance and user activity.

## Complete Example

```typescript
import {
  logDebug,
  logInfo,
  logMetrics,
  logWarning,
  logError,
  logOpenAIRequest,
  logOpenAIResponse,
} from '../services/logging';

function processRequest(request) {
  logInfo(`Processing request: ${request.id}`);

  try {
    // Log detailed information at debug level
    logDebug(`Request details: ${JSON.stringify(request)}`);

    // Log OpenAI-specific information (only shown if LOG_OPENAI_REQUESTS=true)
    logOpenAIRequest(`Sending request to OpenAI: ${request.prompt}`);

    // ... processing logic ...

    // Log metrics information (visible at METRICS level and below)
    logMetrics(`Request ${request.id} processed in ${request.duration}ms`);

    return { success: true };
  } catch (error) {
    // Log errors (always visible)
    logError(
      `Failed to process request: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
    return { success: false };
  }
}
```

## Running with Different Log Levels

```bash
# Show all logs including debug
LOG_LEVEL=DEBUG npm start
# or
METRICS_LOG_LEVEL=0 npm start

# Show only errors
LOG_LEVEL=ERROR npm start
# or
METRICS_LOG_LEVEL=4 npm start
```
