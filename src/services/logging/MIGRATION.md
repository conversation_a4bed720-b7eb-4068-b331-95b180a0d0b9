# Logging System Migration Guide

This guide explains how to migrate existing code to use the new logging system.

## Step 1: Import the logging functions

Add the following import statement to the top of your file:

```typescript
import { logDebug, logInfo, logWarning, logError, logMetrics } from '../services/logging';
```

Adjust the import path as needed based on your file's location.

## Step 2: Replace console.log calls

Replace existing console.log calls with the appropriate logging function:

| Before                               | After                                                                            |
| ------------------------------------ | -------------------------------------------------------------------------------- |
| `console.log('Starting service...')` | `logInfo('Starting service...')`                                                 |
| `console.log('Debug info:', data)`   | `logDebug(`Debug info: ${JSON.stringify(data)}`)`                                |
| `console.warn('Warning message')`    | `logWarning('Warning message')`                                                  |
| `console.error('Error:', error)`     | `logError(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`)` |

## Step 3: Format error messages consistently

When logging errors, use the following pattern to ensure consistent error formatting:

```typescript
try {
  // Your code
} catch (error) {
  logError(
    `Failed to process request: ${error instanceof Error ? error.message : 'Unknown error'}`
  );
}
```

## Step 4: Use helper functions for long strings

When logging long strings, use the helper functions to format them consistently:

```typescript
// Before
console.log('System prompt:', systemPrompt.substring(0, 50) + '...');

// After
import { formatSystemPrompt } from '../services/logging';
logDebug(`System prompt: ${formatSystemPrompt(systemPrompt)}`);
```

## Step 5: Set the log level when running the application

```bash
# Show all logs including debug
METRICS_LOG_LEVEL=0 npm start

# Show only errors
METRICS_LOG_LEVEL=4 npm start
```

## Example: Before and After

### Before

```typescript
import { Server } from './websocket/server';
import { initializeSpeechServices } from './services/speech/initialize';

async function main() {
  try {
    console.log('Starting service...');

    try {
      console.log('Initializing speech services...');
      initializeSpeechServices();
      console.log('Speech services initialized');
    } catch (error) {
      console.error('Failed to initialize services:', error);
      throw error;
    }

    // ...
  } catch (error) {
    console.error('Failed to start service:', error);
    process.exit(1);
  }
}
```

### After

```typescript
import { Server } from './websocket/server';
import { initializeSpeechServices } from './services/speech/initialize';
import { logInfo, logError } from './services/logging';

async function main() {
  try {
    logInfo('Starting service...');

    try {
      logInfo('Initializing speech services...');
      initializeSpeechServices();
      logInfo('Speech services initialized');
    } catch (error) {
      logError(
        `Failed to initialize services: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
      throw error;
    }

    // ...
  } catch (error) {
    logError(
      `Failed to start service: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
    process.exit(1);
  }
}
```
