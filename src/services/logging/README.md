# Logging Service

This module provides a standardized logging system for the application with support for different log levels and colored output.

## Features

- **Color-coded logs** with timestamps for better readability
- **Log levels** (DEBUG, INFO, METRICS, WARN, ERROR)
- **Environment variable control** via `METRICS_LOG_LEVEL`
- **String formatting utilities** for consistent log formatting

## Usage

Import the logging functions:

```typescript
import { logDebug, logInfo, logMetrics, logWarning, logError } from '../services/logging';
```

Use the appropriate log level:

```typescript
logInfo('Starting process');
logDebug('Detailed information for debugging');
logError(`Error occurred: ${error instanceof Error ? error.message : 'Unknown error'}`);
```

## Log Levels

Set the log level using the `METRICS_LOG_LEVEL` environment variable:

- `0`: DEBUG - All logs including detailed debugging information
- `1`: INFO - Informational logs and above
- `2`: METRICS - Only metrics logs and above (default)
- `3`: WARN - Only warnings and errors
- `4`: ERROR - Only errors

## Helper Functions

The module also provides helper functions for formatting log messages:

- `truncateString(str, maxLength)` - Truncates a string if it's too long
- `formatSystemPrompt(prompt)` - Formats a system prompt for logging (truncated)

## Logging of Session and Interaction Statistics

The logging system is also used to record session and interaction statistics from the application's monitoring and metrics subsystem. All key metrics—such as per-interaction timings and conversation summaries—are logged using `logMetrics` at the METRICS log level. This ensures that statistics are consistently formatted, filterable, and production-ready.

### Example: Session and Interaction Statistics

```typescript
// These logs are automatically generated by the monitoring system:
logMetrics(
  '[INTERACTION] ConvID: 123, ReqID: 456, User: "hello", ASR: 120ms, LLM: 300ms, TTS: 80ms, Total: 500ms'
);
logMetrics('[CONVERSATION SUMMARY] ConvID: 123, Interactions: 5');
```

These logs are emitted after each user interaction and when a session is completed, providing a clear record of system performance and user activity.

## Example

```typescript
// In your code
import { logInfo, logDebug, logError } from '../services/logging';

function processRequest(request) {
  logInfo(`Processing request: ${request.id}`);

  try {
    // Log detailed information at debug level
    logDebug(`Request details: ${JSON.stringify(request)}`);

    // ... processing logic ...

    return { success: true };
  } catch (error) {
    // Log errors
    logError(
      `Failed to process request: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
    return { success: false };
  }
}
```

## Running with Different Log Levels

```bash
# Show all logs including debug
METRICS_LOG_LEVEL=0 npm start

# Show only errors
METRICS_LOG_LEVEL=4 npm start
```
