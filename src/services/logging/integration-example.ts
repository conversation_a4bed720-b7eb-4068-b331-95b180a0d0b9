// Example of how to integrate the logging system into an existing file

// Before:
/*
import { Server } from "./websocket/server";
import { initializeSpeechServices } from "./services/speech/initialize";

async function main() {
  try {
    console.log("Starting service...");

    try {
      console.log("Initializing speech services...");
      initializeSpeechServices();
      console.log("Speech services initialized");
    } catch (error) {
      console.error("Failed to initialize services:", error);
      throw error;
    }

    // ...
  } catch (error) {
    console.error("Failed to start service:", error);
    process.exit(1);
  }
}
*/

// After:
// This is a demonstration file only - not meant to be compiled or executed
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { logInfo, logError } from '../monitoring/logging';

// eslint-disable-next-line @typescript-eslint/no-unused-vars
async function main() {
  try {
    logInfo('Starting service...');

    try {
      logInfo('Initializing speech services...');
      // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-empty-function
      const initializeSpeechServices = () => {
        /* Mock function for demonstration */
      };
      initializeSpeechServices();
      logInfo('Speech services initialized');
    } catch (error) {
      logError(
        `Failed to initialize services: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
      throw error;
    }

    // ...
  } catch (error) {
    logError(
      `Failed to start service: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
    process.exit(1);
  }
}

// This is just an example and is not meant to be executed directly
