/* eslint-disable no-console */
// ANSI color codes for terminal output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Log levels
const LOG_LEVEL = {
  DEBUG: 0,
  INFO: 1,
  METRICS: 2,
  WARN: 3,
  ERROR: 4,
};

// Current log level - can be set via environment variable
const currentLogLevel = process.env.METRICS_LOG_LEVEL
  ? parseInt(process.env.METRICS_LOG_LEVEL)
  : LOG_LEVEL.METRICS;

/**
 * Get current timestamp in HH:MM:SS format
 */
function getTimestamp() {
  return new Date().toISOString().substring(11, 19); // Extract HH:MM:SS
}

/**
 * Log debug information (only visible at DEBUG level)
 */
function logDebug(message) {
  if (currentLogLevel <= LOG_LEVEL.DEBUG) {
    console.log(`${colors.dim}[DEBUG ${getTimestamp()}] ${message}${colors.reset}`);
  }
}

/**
 * Log general information (visible at INFO level and below)
 */
function logInfo(message) {
  if (currentLogLevel <= LOG_LEVEL.INFO) {
    console.log(`${colors.cyan}[INFO ${getTimestamp()}] ${message}${colors.reset}`);
  }
}

/**
 * Log metrics data (visible at METRICS level and below)
 */
function logMetrics(message) {
  if (currentLogLevel <= LOG_LEVEL.METRICS) {
    console.log(
      `${colors.green}${colors.bright}[METRICS ${getTimestamp()}] ${message}${colors.reset}`
    );
  }
}

/**
 * Log warning messages (visible at WARN level and below)
 */
function logWarning(message) {
  if (currentLogLevel <= LOG_LEVEL.WARN) {
    console.warn(
      `${colors.yellow}${colors.bright}[WARN ${getTimestamp()}] ${message}${colors.reset}`
    );
  }
}

/**
 * Log error messages (always visible)
 */
function logError(message) {
  console.error(`${colors.red}${colors.bright}[ERROR ${getTimestamp()}] ${message}${colors.reset}`);
}

/**
 * Truncate a string if it's too long
 */
function truncateString(str, maxLength = 30) {
  return str.length > maxLength ? str.substring(0, maxLength - 3) + '...' : str;
}

/**
 * Format a system prompt for logging (truncated)
 */
function formatSystemPrompt(prompt) {
  const lines = prompt.split('\n');
  const firstLine = lines[0] || '';
  return `${truncateString(firstLine, 50)} (${prompt.length} chars)`;
}

// Export the logging functions
module.exports = {
  logDebug,
  logInfo,
  logMetrics,
  logWarning,
  logError,
  truncateString,
  formatSystemPrompt,
  colors,
  LOG_LEVEL,
  currentLogLevel,
  getTimestamp,
};
