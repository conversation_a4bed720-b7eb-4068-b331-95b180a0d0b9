> **DEPRECATION NOTICE:** > `logger.ts` in this directory is a deprecated re-export. The real implementation now resides in `../monitoring/logging`.
> Please update all imports to use `../services/monitoring/logging` for future development.
>
> **Current Structure & Preferred Usage:**
> The logging system is now implemented in `src/services/monitoring/logging.ts`.
> For all new code, import logging functions directly from `../services/monitoring/logging`.
> The old `logger.ts` remains only for backward compatibility and will be removed in a future release.

# Logging Service

This module provides a standardized logging system for the application with support for different log levels, colored output, and configurable verbosity.

## Features

- **Color-coded logs** with timestamps for better readability
- **Log levels** (DEBUG, INFO, METRICS, WARN, ERROR)
- **Environment variable control** via `METRICS_LOG_LEVEL`
- **String formatting utilities** for consistent log formatting
- **Configurable verbosity** to reduce log noise from repetitive operations
- **Rate limiting** for high-frequency log messages

## Log Flooding Protection

To prevent log flooding from repeated identical messages, the logging system now automatically suppresses consecutive duplicate log entries. Only the first occurrence of a repeated message at a given log level is output immediately. Subsequent identical messages are counted but not logged. When a different message is logged (or on flush/shutdown), a summary line is emitted, e.g.:

```
Previous message repeated 7 times
```

This suppression is applied per log level and does not interfere with other logger features such as color-coding, formatting, or rate limiting. All unique messages and their repeat counts are preserved for clarity and troubleshooting.

## Usage

Import the logging functions:

```typescript
import {
  logDebug,
  logInfo,
  logMetrics,
  logWarning,
  logError,
} from '../services/monitoring/logging';
```

Use the appropriate log level:

```typescript
logInfo('Starting process');
logDebug('Detailed information for debugging');
logError(`Error occurred: ${error instanceof Error ? error.message : 'Unknown error'}`);
```

## Log Levels

Set the log level using the `METRICS_LOG_LEVEL` environment variable:

- `0`: DEBUG - All logs including detailed debugging information
- `1`: INFO - Informational logs and above
- `2`: METRICS - Only metrics logs and above (default)
- `3`: WARN - Only warnings and errors
- `4`: ERROR - Only errors

## Helper Functions

The module also provides helper functions for formatting log messages:

- `truncateString(str, maxLength)` - Truncates a string if it's too long
- `formatSystemPrompt(prompt)` - Formats a system prompt for logging (truncated)

## Logging of Session and Interaction Statistics

The logging system is also used to record session and interaction statistics from the application's monitoring and metrics subsystem. All key metrics—such as per-interaction timings and conversation summaries—are logged using `logMetrics` at the METRICS log level. This ensures that statistics are consistently formatted, filterable, and production-ready.

### Example: Session and Interaction Statistics

```typescript
// These logs are automatically generated by the monitoring system:
logMetrics(
  '[INTERACTION] ConvID: 123, ReqID: 456, User: "hello", ASR: 120ms, LLM: 300ms, TTS: 80ms, Total: 500ms'
);
logMetrics('[CONVERSATION SUMMARY] ConvID: 123, Interactions: 5');
```

These logs are emitted after each user interaction and when a session is completed, providing a clear record of system performance and user activity.

## Example

```typescript
// In your code
import { logInfo, logDebug, logError } from '../services/monitoring/logging';

function processRequest(request) {
  logInfo(`Processing request: ${request.id}`);

  try {
    // Log detailed information at debug level
    logDebug(`Request details: ${JSON.stringify(request)}`);

    // ... processing logic ...

    return { success: true };
  } catch (error) {
    // Log errors
    logError(
      `Failed to process request: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
    return { success: false };
  }
}
```

## Running with Different Log Levels

```bash
# Show all logs including debug
METRICS_LOG_LEVEL=0 npm start

# Show only errors
METRICS_LOG_LEVEL=4 npm start
```

## Configurable Verbosity

The logging system includes configuration options to control the verbosity of logs, especially for high-frequency operations. This helps reduce log noise while maintaining important information.

### Configuration Options

The configuration is defined in `logging-config.ts`:

```typescript
export const loggingConfig = {
  // How often to log repeated messages (in milliseconds)
  // Set to 0 to log every occurrence
  botServiceCheckInterval: 5000, // Log "Checking if bot exists" every 5 seconds
  asrAudioChunkInterval: 2000, // Log audio chunk processing every 2 seconds
  webSocketMessageInterval: 1000, // Log WebSocket messages every 1 second

  // Whether to log every state transition or only important ones
  logAllStateTransitions: false,

  // Whether to log detailed ASR processing information
  logDetailedASRProcessing: false,

  // Whether to log duplicate transcript checks
  logDuplicateChecks: false,
};
```

### Rate Limiting Logs

To prevent log flooding from repetitive operations, use the `shouldLogMessage` utility:

```typescript
import { shouldLogMessage } from '../services/logging/logging-config';

// Only log this message every 5 seconds
if (shouldLogMessage('my-operation', 5000)) {
  logInfo('Operation is running');
}
```

This is particularly useful for:

- High-frequency operations like audio chunk processing
- Repetitive checks like "Checking if bot exists"
- WebSocket message sending
- State transitions that occur frequently
