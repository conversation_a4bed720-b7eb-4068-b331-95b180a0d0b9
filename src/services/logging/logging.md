# Logging System

This document provides an overview of the application's logging system.

## Architecture

The logging system is organized as follows:

- **Core Implementation**: Located in `src/services/monitoring/logging.ts`
- **Configuration**: Located in `src/services/logging/logging-config.ts`
- **Compatibility Layer**: Files in `src/services/logging` re-export from the core implementation

For all new code, import logging functions from `../services/monitoring/logging`.

## Key Features

- **Color-coded logs** with timestamps for better readability
- **Hierarchical log levels** (DEBUG, INFO, METRICS, WARN, ERROR)
- **Flexible environment variable control** via `LOG_LEVEL` or `METRICS_LOG_LEVEL`
- **Category-specific logging** for HTTP, OpenAI, WebSocket, and ASR
- **Rate-limited logging** to reduce noise from high-frequency events
- **Formatting utilities** for consistent log presentation

## Log Levels

The system supports both string-based and numeric log levels:

### String-based (via `LOG_LEVEL`)

- `DEBUG` - All logs including detailed debugging information
- `INFO` - Informational logs and above
- `METRICS` - Only metrics logs and above (default)
- `WARN` - Only warnings and errors
- `ERROR` - Only errors

### Numeric (via `METRICS_LOG_LEVEL`)

- `0`: DEBUG
- `1`: INFO
- `2`: METRICS (default)
- `3`: WARN
- `4`: ERROR

## Category-Specific Logging

Enable or disable specific log categories using environment variables:

```
LOG_HTTP_REQUESTS=true
LOG_HTTP_RESPONSES=true
LOG_OPENAI_REQUESTS=true
LOG_OPENAI_RESPONSES=true
LOG_WEBSOCKET_MESSAGES=true
LOG_ASR_INTERIM=true
```

The system provides specialized logging functions that only output logs when their corresponding environment variable is set to `true`.

## Rate-Limited Logging

The logging system includes rate-limiting functionality to reduce noise from high-frequency events. This is particularly useful for WebSocket messages, audio processing, and other operations that occur frequently.

### Configuration

Rate-limiting settings are defined in `src/services/logging/logging-config.ts` with the following default intervals:

- `botServiceCheckInterval`: 5000ms (Log "Checking if bot exists" every 5 seconds)
- `asrAudioChunkInterval`: 2000ms (Log audio chunk processing every 2 seconds)
- `webSocketMessageInterval`: 1000ms (Log WebSocket messages every 1 second)

The `shouldLogMessage` function takes a unique key to identify the message type and the minimum interval between logs in milliseconds. The `resetMessageTimestamp` function can be used to reset the rate limit for a specific message type.

## Helper Functions

The module provides helper functions for formatting log messages:

- `truncateString(str, maxLength)` - Truncates a string if it's too long
- `formatSystemPrompt(prompt)` - Formats a system prompt for logging (truncated)

## Metrics and Performance Monitoring

The logging system is used to record session and interaction statistics from the application's monitoring and metrics subsystem. All key metrics—such as per-interaction timings and conversation summaries—are logged using `logMetrics` at the METRICS log level.

These logs are emitted after each user interaction and when a session is completed, providing a clear record of system performance and user activity.

## Running with Different Log Levels

To adjust the verbosity of logs, set the appropriate environment variable:

```bash
# Show all logs including debug
LOG_LEVEL=DEBUG npm start
# or
METRICS_LOG_LEVEL=0 npm start

# Show only errors
LOG_LEVEL=ERROR npm start
# or
METRICS_LOG_LEVEL=4 npm start
```

## Recent Improvements

### WebSocketController Rate-Limited Logging

The WebSocketController has been updated to use rate-limited logging to reduce noise from high-frequency WebSocket operations. This implementation ensures that log messages for WebSocket operations are only emitted at the configured intervals, significantly reducing log noise while still providing visibility into system operations.
