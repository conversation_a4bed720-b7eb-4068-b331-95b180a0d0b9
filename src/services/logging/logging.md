# Logging System – Unified Guide

This document describes the logging system for developers and coding AIs. It covers architecture, usage, configuration, and extension points.

---

## Architecture

- **Core:** [`src/services/monitoring/logging.ts`](../monitoring/logging.ts)
- **Exports:** [`src/services/logging/index.ts`](index.ts) (for backward compatibility)
- **Deprecation:** `logger.ts` is deprecated. Use `../services/logging`.

---

## Features

- Color-coded logs with timestamps
- Log levels: DEBUG, INFO, METRICS, WARN, ERROR
- Environment variable control (`LOG_LEVEL` or `METRICS_LOG_LEVEL`)
- Category-specific logging (HTTP, OpenAI, WebSocket, ASR, etc.)
- Log flooding protection (suppresses repeated identical messages)
- Configurable verbosity/rate limiting
- Helper functions for formatting/truncation

---

## Usage

**Import:**
```typescript
import {
  logDebug, logInfo, logMetrics, logWarning, logError,
  logOpenAIRequest, logOpenAIResponse,
  logHttpRequest, logHttpResponse,
  logWebSocketMessage, logASRInterim
} from '../services/logging';
```

**Basic Example:**
```typescript
logInfo('Starting process');
logDebug('Debug details');
logError('Something went wrong');
logMetrics('[INTERACTION] ...');
```

**Category Example:**
```typescript
logOpenAIRequest('Prompt sent');
logHttpRequest('API call');
logWebSocketMessage('WS event');
logASRInterim('Partial transcript');
```

---

## Log Levels

| String (`LOG_LEVEL`) | Numeric (`METRICS_LOG_LEVEL`) | Description                    |
|----------------------|-------------------------------|--------------------------------|
| DEBUG                | 0                             | All logs                       |
| INFO                 | 1                             | Info and above                 |
| METRICS (default)    | 2                             | Metrics and above              |
| WARN                 | 3                             | Warnings and errors            |
| ERROR                | 4                             | Errors only                    |

**Set via env:**
```bash
LOG_LEVEL=DEBUG npm start
# or
METRICS_LOG_LEVEL=0 npm start
```

---

## Category-Specific Logging

Enable with env vars:
```bash
LOG_HTTP_REQUESTS=true
LOG_HTTP_RESPONSES=true
LOG_OPENAI_REQUESTS=true
LOG_OPENAI_RESPONSES=true
LOG_WEBSOCKET_MESSAGES=true
LOG_ASR_INTERIM=true
```
Only logs if the corresponding variable is `true`.

---

## Log Flooding Protection

- Suppresses consecutive identical messages per log level.
- Emits: `Previous message repeated N times` when a new message appears or on flush.

---

## Configurable Verbosity & Rate Limiting

**Config:** [`logging-config.ts`](logging-config.ts)
```typescript
export const loggingConfig = {
  botServiceCheckInterval: 5000,
  asrAudioChunkInterval: 2000,
  webSocketMessageInterval: 1000,
  logAllStateTransitions: false,
  logDetailedASRProcessing: false,
  logDuplicateChecks: false,
};
```
**Rate limit utility:**
```typescript
import { shouldLogMessage } from '../services/logging/logging-config';
if (shouldLogMessage('my-op', 5000)) logInfo('...');
```

---

## Helper Functions

- `truncateString(str, maxLength)`
- `formatSystemPrompt(prompt)`

---

## Metrics & Session Logging

Use `logMetrics` for key metrics and summaries:
```typescript
logMetrics('[INTERACTION] ConvID:..., ASR: 120ms, ...');
logMetrics('[CONVERSATION SUMMARY] ConvID:..., Interactions: 5');
```

---

## Migration & Compatibility

- **Old logger.ts is deprecated.** Use `../services/logging`.
- Compatibility layer will be removed in a future release.

---

## Testing

- See [`src/services/monitoring/__tests__/`](../../monitoring/__tests__) for unit tests.
- Test with different log levels and category toggles.

---

## Implementation Checklist

- [x] Unified logging in `monitoring/logging.ts`
- [x] Compatibility layer
- [x] Log flooding protection
- [x] Category logging
- [x] Configurable verbosity/rate limiting
- [x] Helper functions
- [x] Metrics/statistics logging
- [x] Unified documentation

---

## Improvement Ideas

| Idea                                 | Complexity | Importance | Notes                                    |
|-------------------------------------- |----------- |----------- |------------------------------------------|
| Centralized config UI/CLI            | Medium     | Medium     | Adjust log levels/categories at runtime  |
| Structured (JSON) log output         | Med-High   | High       | For log aggregation tools                |
| Per-module log level overrides       | Medium     | Medium     | More granular control                    |
| External log sinks (syslog/cloud)    | High       | Med-High   | For production deployments               |
| Automated log rotation/archiving     | Medium     | Medium     | For persistent logs                      |

---

**See [`src/services/monitoring/logging.ts`](../monitoring/logging.ts) and [`logging-config.ts`](logging-config.ts) for implementation details.**
