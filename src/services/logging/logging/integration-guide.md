# Logging System Integration Guide

This guide explains how to integrate the logging system into the application.

## Step 1: Add the logging module to your TypeScript project

1. Copy the `logging.ts` file to the `src/services/monitoring` directory.
2. Update the `src/services/monitoring/index.ts` file to export the logging functions:

```typescript
export * from './logging';
```

## Step 2: Build the project

```bash
npm run build
```

## Step 3: Use the logging functions in your code

Replace console.log calls with the appropriate logging function:

```typescript
// Before
console.log('Starting service...');
console.error('Failed to process request:', error);

// After
import { logInfo, logError } from '../services/monitoring/logging';

logInfo('Starting service...');
logError(`Failed to process request: ${error instanceof Error ? error.message : 'Unknown error'}`);
```

## Step 4: Set the log level when running the application

```bash
# Show all logs including debug
METRICS_LOG_LEVEL=0 npm start

# Show only errors
METRICS_LOG_LEVEL=4 npm start
```

## Example: Updating a file to use the logging system

Here's an example of updating a file to use the logging system:

```typescript
// Before
import { Server } from './websocket/server';
import { initializeSpeechServices } from './services/speech/initialize';

async function main() {
  try {
    console.log('Starting service...');

    try {
      console.log('Initializing speech services...');
      initializeSpeechServices();
      console.log('Speech services initialized');
    } catch (error) {
      console.error('Failed to initialize services:', error);
      throw error;
    }

    // ...
  } catch (error) {
    console.error('Failed to start service:', error);
    process.exit(1);
  }
}
```

```typescript
// After
import { Server } from './websocket/server';
import { initializeSpeechServices } from './services/speech/initialize';
import { logInfo, logError } from './services/monitoring/logging';

async function main() {
  try {
    logInfo('Starting service...');

    try {
      logInfo('Initializing speech services...');
      initializeSpeechServices();
      logInfo('Speech services initialized');
    } catch (error) {
      logError(
        `Failed to initialize services: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
      throw error;
    }

    // ...
  } catch (error) {
    logError(
      `Failed to start service: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
    process.exit(1);
  }
}
```
