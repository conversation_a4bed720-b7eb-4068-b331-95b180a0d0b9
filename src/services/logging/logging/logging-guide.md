# Logging System Guide

This document provides a concise guide to the logging system.

## Core Features

1. **Color-coded logs** with timestamps
2. **Log levels** (DEBUG, INFO, METRICS, WARN, ERROR)
3. **Environment variable control** via `METRICS_LOG_LEVEL`

## Usage

Import the logging functions:

```typescript
import {
  logDebug,
  logInfo,
  logMetrics,
  logWarning,
  logError,
} from '../services/monitoring/logging';
```

Use the appropriate log level:

```typescript
logInfo('Starting process');
logDebug('Detailed information for debugging');
logError('Something went wrong');
```

## Log Levels

Set the log level using the `METRICS_LOG_LEVEL` environment variable:

- `0`: DEBUG - All logs including detailed debugging information
- `1`: INFO - Informational logs and above
- `2`: METRICS - Only metrics logs and above (default)
- `3`: WARN - Only warnings and errors
- `4`: ERROR - Only errors

## Example

```typescript
// In your code
logInfo(`Initialized service with ${options.length} options`);
logDebug(`Configuration details: ${JSON.stringify(config)}`);

// If something goes wrong
try {
  // Your code
} catch (error) {
  logError(
    `Failed to process request: ${error instanceof Error ? error.message : 'Unknown error'}`
  );
}
```

## Running with Different Log Levels

```bash
# Show all logs including debug
METRICS_LOG_LEVEL=0 npm start

# Show only errors
METRICS_LOG_LEVEL=4 npm start
```
