import { logInfo } from './logging';
import { ChatMessage } from '../bot-service/types';

/**
 * OpenAI log format options
 */
export enum OpenAILogFormat {
  MINIMAL = 'minimal',
  STANDARD = 'standard',
  DETAILED = 'detailed',
}

/**
 * Get the current OpenAI log format from environment variables
 * @returns The current OpenAI log format
 */
export function getOpenAILogFormat(): OpenAILogFormat {
  // TEMPORARY DIAGNOSTIC LOGGING
  // eslint-disable-next-line no-console
  console.log('[DIAG] getOpenAILogFormat called');
  // eslint-disable-next-line no-console
  console.log('[DIAG] process.env.OPENAI_LOG_FORMAT:', process.env.OPENAI_LOG_FORMAT);

  const format = process.env.OPENAI_LOG_FORMAT?.toLowerCase();
  // eslint-disable-next-line no-console
  console.log('[DIAG] computed format:', format);

  if (format === OpenAILogFormat.MINIMAL) {
    // eslint-disable-next-line no-console
    console.log('[DIAG] Returning OpenAILogFormat.MINIMAL');
    return OpenAILogFormat.MINIMAL;
  } else if (format === OpenAILogFormat.DETAILED) {
    // eslint-disable-next-line no-console
    console.log('[DIAG] Returning OpenAILogFormat.DETAILED');
    return OpenAILogFormat.DETAILED;
  } else {
    // eslint-disable-next-line no-console
    console.log('[DIAG] Returning OpenAILogFormat.STANDARD');
    return OpenAILogFormat.STANDARD;
  }
}

/**
 * Log an OpenAI request in the configured format
 * @param conversationId The conversation ID
 * @param ani The ANI (caller ID)
 * @param messages The conversation messages
 * @param model The model being used (optional)
 * @param requestPayload The full request payload (optional)
 */
export function logOpenAIRequest(
  conversationId: string | undefined,
  ani: string | undefined,
  messages: ChatMessage[],
  model?: string,
  requestPayload?: any
): void {
  const format = getOpenAILogFormat();

  // Extract just the user messages for minimal format
  const userMessages = messages
    .filter(msg => msg.role === 'user')
    .map(msg => msg.content)
    .filter(Boolean);

  switch (format) {
    case OpenAILogFormat.MINIMAL:
      // Only log the last user message for extreme brevity
      const lastUserMessage = userMessages.length > 0 ? userMessages[userMessages.length - 1] : '';
      logInfo(
        `[OpenAI Request] User: "${
          lastUserMessage ? lastUserMessage.substring(0, 30) + '...' : ''
        }"`
      );
      break;

    case OpenAILogFormat.STANDARD:
      logInfo(
        `[OpenAI Request] ConvID: ${conversationId || 'unknown'}, ANI: ${
          ani || 'unknown'
        }, Model: ${model || 'unknown'}, Messages: ${JSON.stringify(
          messages.map(m => ({ role: m.role, content: m.content }))
        )}`
      );
      break;

    case OpenAILogFormat.DETAILED:
      logInfo(`[OpenAI Request] ConvID: ${conversationId || 'unknown'}, ANI: ${ani || 'unknown'}`);
      logInfo(`- Model: ${model || 'unknown'}`);
      logInfo(`- Messages: ${JSON.stringify(messages, null, 2)}`);
      if (requestPayload) {
        logInfo(`- Full Request Payload: ${JSON.stringify(requestPayload, null, 2)}`);
      }
      break;
  }
}

/**
 * Log an OpenAI response in the configured format
 * @param conversationId The conversation ID
 * @param response The response content
 * @param fullResponse The full response object (optional)
 */
export function logOpenAIResponse(
  conversationId: string | undefined,
  response: string,
  fullResponse?: any
): void {
  const format = getOpenAILogFormat();

  switch (format) {
    case OpenAILogFormat.MINIMAL:
      // For minimal format, just log the first 30 characters of the response
      logInfo(
        `[OpenAI Response] AI: "${
          response.length > 30 ? response.substring(0, 30) + '...' : response
        }"`
      );
      break;

    case OpenAILogFormat.STANDARD:
      logInfo(`[OpenAI Response] ConvID: ${conversationId || 'unknown'}, Response: ${response}`);
      break;

    case OpenAILogFormat.DETAILED:
      logInfo(`[OpenAI Response] ConvID: ${conversationId || 'unknown'}`);
      logInfo(`- Response: ${response}`);
      if (fullResponse) {
        logInfo(`- Full Response: ${JSON.stringify(fullResponse, null, 2)}`);
      }
      break;
  }
}
