/* eslint-disable no-console */
// Test script for the logging system
const {
  logDebug,
  logInfo,
  logMetrics,
  logWarning,
  logError,
  truncateString,
  formatSystemPrompt,
} = require('./logger');

console.log('Starting logging test...');

// Test each log level
logDebug('This is a debug message');
logInfo('This is an info message');
logMetrics('This is a metrics message');
logWarning('This is a warning message');
logError('This is an error message');

// Test string truncation
const longString = 'This is a very long string that should be truncated when logged';
console.log('\nTesting string truncation:');
console.log(`Original: "${longString}"`);
console.log(`Truncated: "${truncateString(longString)}"`);
console.log(`Truncated (20 chars): "${truncateString(longString, 20)}"`);

// Test system prompt formatting
const systemPrompt = `This is a system prompt with multiple lines.
It contains instructions for the AI assistant.
It should be formatted properly when logged.`;
console.log('\nTesting system prompt formatting:');
console.log(`Original:\n${systemPrompt}`);
console.log(`Formatted: ${formatSystemPrompt(systemPrompt)}`);

console.log('\nLogging test complete.');
