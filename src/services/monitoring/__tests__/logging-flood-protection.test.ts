import {
  logInfo,
  logDebug,
  logWarning,
  logError,
  flushSuppressedLogs,
} from '../../logging/logging';

describe('Log Flooding Protection', () => {
  let consoleLogSpy: jest.SpyInstance;
  let consoleWarnSpy: jest.SpyInstance;
  let consoleErrorSpy: jest.SpyInstance;

  beforeEach(() => {
    consoleLogSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
    consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation(() => {});
    consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    consoleLogSpy.mockRestore();
    consoleWarnSpy.mockRestore();
    consoleErrorSpy.mockRestore();
    flushSuppressedLogs(); // Clean up state
  });

  it('suppresses repeated info messages and logs summary', () => {
    logInfo('hello');
    logInfo('hello');
    logInfo('hello');
    logInfo('world');
    flushSuppressedLogs();

    // Should log: "hello", summary for "hello" (repeated 2 times), "world"
    const logs = consoleLogSpy.mock.calls.map(call => call[0]);
    expect(logs.some(line => line.includes('hello'))).toBe(true);
    expect(logs.some(line => line.includes('Previous message repeated 2 times:'))).toBe(true);
    expect(logs.some(line => line.includes('world'))).toBe(true);
  });

  it('logs summary on flush if last message was repeated', () => {
    logWarning('warn1');
    logWarning('warn1');
    flushSuppressedLogs();

    // Should log: "warn1", summary for "warn1" (repeated 1 time)
    const warns = consoleWarnSpy.mock.calls.map(call => call[0]);
    expect(warns.some(line => line.includes('warn1'))).toBe(true);
    expect(warns.some(line => line.includes('Previous message repeated 1 times:'))).toBe(true);
  });

  it('does not log summary if no repeats', () => {
    logDebug('a');
    logDebug('b');
    flushSuppressedLogs();

    // Should log: "a", "b", but no summary
    const logs = consoleLogSpy.mock.calls.map(call => call[0]);
    expect(logs.some(line => line.includes('a'))).toBe(true);
    expect(logs.some(line => line.includes('b'))).toBe(true);
    expect(logs.every(line => !line.includes('Previous message repeated'))).toBe(true);
  });

  it('always logs errors', () => {
    // Reset the log flood state
    flushSuppressedLogs();

    // Log the same error multiple times
    logError('err');
    logError('err');
    logError('err');

    // Log a different error to trigger the summary
    logError('err2');

    // Flush any remaining suppressed logs
    flushSuppressedLogs();

    // Should log both error messages
    const errors = consoleErrorSpy.mock.calls.map(call => call[0]);

    // Debug: print all error messages
    console.log('Actual error messages:', errors);

    // Check that both error messages were logged
    expect(errors.some(line => line.includes('err'))).toBe(true);
    expect(errors.some(line => line.includes('err2'))).toBe(true);

    // Check that we have at least 2 error messages
    expect(errors.length).toBeGreaterThanOrEqual(2);
  });
});
