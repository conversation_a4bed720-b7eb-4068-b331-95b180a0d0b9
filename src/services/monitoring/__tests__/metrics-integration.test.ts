import { PerformanceLogger, PhaseContext, RequestContext } from '../performance-logger';
import { MetricsPhase } from '../../../types/monitoring';

// Mock the PerformanceMetricsStore
jest.mock('../performance-metrics-store', () => {
  return {
    PerformanceMetricsStore: {
      getInstance: jest.fn().mockReturnValue({
        initializeConversation: jest.fn(),
        initializeRequest: jest.fn().mockReturnValue('mock-request-id'),
        startPhase: jest.fn(),
        endPhase: jest.fn(),
        finalizeRequest: jest.fn(),
        finalizeConversation: jest.fn(),
        getConversationMetrics: jest.fn().mockReturnValue(null),
        setUserInput: jest.fn(),
        setAiReply: jest.fn(),
      }),
    },
  };
});

describe('Metrics Integration', () => {
  let performanceLogger: PerformanceLogger;

  beforeEach(() => {
    // Reset the singleton instance
    // @ts-ignore - accessing private property for testing
    PerformanceLogger.instance = undefined;

    // Get a fresh instance
    performanceLogger = PerformanceLogger.getInstance();

    // Set up conversation and request
    performanceLogger.setConversationId('test-conv-id');
    performanceLogger.initializeRequest();

    // @ts-ignore - accessing private property for testing
    performanceLogger.currentRequestId = 'test-req-id';

    // Enable metrics for testing
    process.env.ENABLE_PERF_MONITOR = 'true';

    // Spy on methods
    jest.spyOn(performanceLogger, 'startPhase');
    jest.spyOn(performanceLogger, 'endPhase');
    jest.spyOn(performanceLogger, 'setCurrentUserInput');
    jest.spyOn(performanceLogger, 'setCurrentAiReply');
    jest.spyOn(performanceLogger, 'finalizeRequest');
  });

  afterEach(() => {
    // Clean up environment variables
    delete process.env.ENABLE_PERF_MONITOR;

    // Reset all mocks
    jest.clearAllMocks();
  });

  describe('PhaseContext', () => {
    it('should automatically start and end phases', () => {
      // Use the PhaseContext in a try-finally block
      const phase: MetricsPhase = 'speechToText';
      let phaseContext: PhaseContext | null = null;

      try {
        // Create the PhaseContext
        phaseContext = performanceLogger.trackPhase(phase);

        // Verify that startPhase was called
        expect(performanceLogger.startPhase).toHaveBeenCalledWith(phase);
        expect(performanceLogger.endPhase).not.toHaveBeenCalled();
      } finally {
        // End the phase
        if (phaseContext) {
          phaseContext.end();
        }
      }

      // Verify that endPhase was called
      expect(performanceLogger.endPhase).toHaveBeenCalledWith(phase);
    });

    it('should handle errors gracefully', () => {
      // Use the PhaseContext with an error
      const phase: MetricsPhase = 'speechToText';
      let phaseContext: PhaseContext | null = null;

      try {
        // Create the PhaseContext
        phaseContext = performanceLogger.trackPhase(phase);

        // Throw an error
        throw new Error('Test error');
      } catch (error) {
        // Error should be caught
        expect(error).toBeDefined();
      } finally {
        // End the phase
        if (phaseContext) {
          phaseContext.end();
        }
      }

      // Verify that both startPhase and endPhase were called
      expect(performanceLogger.startPhase).toHaveBeenCalledWith(phase);
      expect(performanceLogger.endPhase).toHaveBeenCalledWith(phase);
    });
  });

  describe('transitionPhase', () => {
    it('should end one phase and start another', () => {
      // Use transitionPhase to switch from ASR to LLM
      const fromPhase: MetricsPhase = 'speechToText';
      const toPhase: MetricsPhase = 'llmProcessing';

      // Start the initial phase
      performanceLogger.startPhase(fromPhase);

      // Reset the spy counts
      jest.clearAllMocks();

      // Transition to the next phase
      const phaseContext = performanceLogger.transitionPhase(fromPhase, toPhase);

      // Verify that endPhase was called for the first phase
      expect(performanceLogger.endPhase).toHaveBeenCalledWith(fromPhase);

      // Verify that startPhase was called for the second phase
      expect(performanceLogger.startPhase).toHaveBeenCalledWith(toPhase);

      // End the second phase
      phaseContext.end();

      // Verify that endPhase was called for the second phase
      expect(performanceLogger.endPhase).toHaveBeenCalledWith(toPhase);
    });
  });

  describe('RequestContext', () => {
    it('should create a request context with the correct request ID', () => {
      // Mock initializeRequest to return a predictable value
      jest.spyOn(performanceLogger, 'initializeRequest').mockReturnValue('new-req-id');

      // Create a request context
      const requestContext = performanceLogger.createRequest();

      // Verify that initializeRequest was called
      expect(performanceLogger.initializeRequest).toHaveBeenCalled();

      // Verify that the request ID is correct
      expect(requestContext.requestId).toBe('new-req-id');
    });

    it('should set user input and AI reply', () => {
      // Create a request context
      const requestContext = performanceLogger.createRequest();

      // Set user input and AI reply
      requestContext.setUserInput('test user input');
      requestContext.setAiReply('test AI reply');

      // Verify that the methods were called with the correct values
      expect(performanceLogger.setCurrentUserInput).toHaveBeenCalledWith('test user input');
      expect(performanceLogger.setCurrentAiReply).toHaveBeenCalledWith('test AI reply');
    });

    it('should track phases', () => {
      // Create a request context
      const requestContext = performanceLogger.createRequest();

      // Track a phase
      const phaseContext = requestContext.trackPhase('speechToText');

      // Verify that startPhase was called
      expect(performanceLogger.startPhase).toHaveBeenCalledWith('speechToText');

      // End the phase
      phaseContext.end();

      // Verify that endPhase was called
      expect(performanceLogger.endPhase).toHaveBeenCalledWith('speechToText');
    });

    it('should transition between phases', () => {
      // Create a request context
      const requestContext = performanceLogger.createRequest();

      // Start the initial phase
      performanceLogger.startPhase('speechToText');

      // Reset the spy counts
      jest.clearAllMocks();

      // Transition to the next phase
      const phaseContext = requestContext.transitionPhase('speechToText', 'llmProcessing');

      // Verify that endPhase was called for the first phase
      expect(performanceLogger.endPhase).toHaveBeenCalledWith('speechToText');

      // Verify that startPhase was called for the second phase
      expect(performanceLogger.startPhase).toHaveBeenCalledWith('llmProcessing');

      // End the second phase
      phaseContext.end();

      // Verify that endPhase was called for the second phase
      expect(performanceLogger.endPhase).toHaveBeenCalledWith('llmProcessing');
    });

    it('should finalize the request', () => {
      // Create a request context
      const requestContext = performanceLogger.createRequest();

      // Finalize the request
      requestContext.finalize();

      // Verify that finalizeRequest was called
      expect(performanceLogger.finalizeRequest).toHaveBeenCalled();
    });

    it('should finalize the request with an AI reply', () => {
      // Create a request context
      const requestContext = performanceLogger.createRequest();

      // Finalize the request with an AI reply
      requestContext.finalize('test AI reply');

      // Verify that setCurrentAiReply and finalizeRequest were called
      expect(performanceLogger.setCurrentAiReply).toHaveBeenCalledWith('test AI reply');
      expect(performanceLogger.finalizeRequest).toHaveBeenCalled();
    });

    it('should support a fluent API', () => {
      // Create a request context
      const requestContext = performanceLogger.createRequest();

      // Use the fluent API
      requestContext.setUserInput('test user input').setAiReply('test AI reply').finalize();

      // Verify that the methods were called with the correct values
      expect(performanceLogger.setCurrentUserInput).toHaveBeenCalledWith('test user input');
      expect(performanceLogger.setCurrentAiReply).toHaveBeenCalledWith('test AI reply');
      expect(performanceLogger.finalizeRequest).toHaveBeenCalled();
    });
  });
});
