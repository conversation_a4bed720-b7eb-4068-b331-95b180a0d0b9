import { PerformanceMetricsStore } from '../performance-metrics-store';
import { MetricsPhase } from '../../../types/monitoring';

// Mock the logger to avoid console output during tests
jest.mock('../../logging/logging', () => ({
  logMetrics: jest.fn(),
}));

describe('PerformanceMetricsStore', () => {
  let metricsStore: PerformanceMetricsStore;
  const testConversationId = 'test-conv-id';
  const testRequestId = 'test-req-id';
  const testAni = 'test-ani';
  const testUserInput = 'test user input';
  const testAiReply = 'test AI reply';

  beforeEach(() => {
    // Reset the singleton instance before each test
    // @ts-ignore - accessing private property for testing
    PerformanceMetricsStore.instance = undefined;
    metricsStore = PerformanceMetricsStore.getInstance();

    // Enable metrics for testing
    process.env.ENABLE_PERF_MONITOR = 'true';
    process.env.METRICS_LOG_LEVEL = '0';
  });

  afterEach(() => {
    // Clean up environment variables
    delete process.env.ENABLE_PERF_MONITOR;
    delete process.env.METRICS_LOG_LEVEL;
  });

  describe('getInstance', () => {
    it('should return the same instance when called multiple times', () => {
      const instance1 = PerformanceMetricsStore.getInstance();
      const instance2 = PerformanceMetricsStore.getInstance();
      expect(instance1).toBe(instance2);
    });
  });

  describe('initializeConversation', () => {
    it('should initialize conversation metrics with the correct structure', () => {
      metricsStore.initializeConversation(testConversationId, testAni);

      const metrics = metricsStore.getConversationMetrics(testConversationId);

      expect(metrics).toBeDefined();
      assertMetrics(metrics);
      expect(metrics.conversationId).toBe(testConversationId);
      expect(metrics.ani).toBe(testAni);
      expect(metrics.requests).toEqual([]);
      expect(metrics.phaseMetrics).toBeDefined();
      expect(metrics.phaseMetrics.speechToText).toBeDefined();
      expect(metrics.phaseMetrics.llmProcessing).toBeDefined();
      expect(metrics.phaseMetrics.textToSpeech).toBeDefined();
    });
  });

  describe('initializeRequest', () => {
    beforeEach(() => {
      metricsStore.initializeConversation(testConversationId, testAni);
    });

    it('should initialize request metrics with the correct structure', () => {
      metricsStore.initializeRequest(testConversationId, testRequestId);

      const metrics = metricsStore.getConversationMetrics(testConversationId);

      assertMetrics(metrics);
      expect(metrics.requests.length).toBe(1);
      expect(metrics.requests[0].requestId).toBe(testRequestId);
      expect(metrics.requests[0].userInput).toBe('');
      expect(metrics.requests[0].aiReply).toBe('');
      expect(metrics.requests[0].phases).toBeDefined();
      expect(metrics.requests[0].phases.speechToText).toBeDefined();
      expect(metrics.requests[0].phases.llmProcessing).toBeDefined();
      expect(metrics.requests[0].phases.textToSpeech).toBeDefined();
    });

    it('should do nothing if conversation does not exist', () => {
      metricsStore.initializeRequest('non-existent-id', testRequestId);

      const metrics = metricsStore.getConversationMetrics(testConversationId);
      assertMetrics(metrics);
      expect(metrics.requests.length).toBe(0);
    });
  });

  describe('startPhase and endPhase', () => {
    beforeEach(() => {
      metricsStore.initializeConversation(testConversationId, testAni);
      metricsStore.initializeRequest(testConversationId, testRequestId);

      // Reset mocks
      jest.restoreAllMocks();
    });

    it('should start and end a phase correctly', () => {
      // Create a mock implementation for Date.now
      const mockNow = jest.spyOn(Date, 'now');

      // Set the start time
      mockNow.mockReturnValueOnce(1000);
      metricsStore.startPhase(testConversationId, testRequestId, 'speechToText');

      // Set the end time
      mockNow.mockReturnValueOnce(1500);
      metricsStore.endPhase(testConversationId, testRequestId, 'speechToText');

      // Get the metrics
      const metrics = metricsStore.getConversationMetrics(testConversationId);
      assertMetrics(metrics);

      // Manually calculate the duration
      const phase = metrics.requests[0].phases.speechToText;
      const startTime = phase.startTime.getTime();
      const endTime = phase.endTime!.getTime();
      const calculatedDuration = endTime - startTime;

      // Check that the duration matches our calculation
      expect(phase.duration).toBe(calculatedDuration);

      // Check conversation-level metrics
      expect(metrics.phaseMetrics.speechToText.count).toBe(1);
      expect(metrics.phaseMetrics.speechToText.totalDuration).toBe(calculatedDuration);
      expect(metrics.phaseMetrics.speechToText.avgDuration).toBe(calculatedDuration);
    });

    it('should handle multiple phases correctly', () => {
      // Create a mock implementation for Date.now
      const mockNow = jest.spyOn(Date, 'now');

      // Set the ASR start time
      mockNow.mockReturnValueOnce(1000);
      metricsStore.startPhase(testConversationId, testRequestId, 'speechToText');

      // Set the ASR end time
      mockNow.mockReturnValueOnce(1500);
      metricsStore.endPhase(testConversationId, testRequestId, 'speechToText');

      // Set the LLM start time
      mockNow.mockReturnValueOnce(2000);
      metricsStore.startPhase(testConversationId, testRequestId, 'llmProcessing');

      // Set the LLM end time
      mockNow.mockReturnValueOnce(3000);
      metricsStore.endPhase(testConversationId, testRequestId, 'llmProcessing');

      // Get the metrics
      const metrics = metricsStore.getConversationMetrics(testConversationId);
      assertMetrics(metrics);

      // Manually calculate the durations
      const asrPhase = metrics.requests[0].phases.speechToText;
      const asrStartTime = asrPhase.startTime.getTime();
      const asrEndTime = asrPhase.endTime!.getTime();
      const asrDuration = asrEndTime - asrStartTime;

      const llmPhase = metrics.requests[0].phases.llmProcessing;
      const llmStartTime = llmPhase.startTime.getTime();
      const llmEndTime = llmPhase.endTime!.getTime();
      const llmDuration = llmEndTime - llmStartTime;

      // Check that the durations match our calculations
      expect(asrPhase.duration).toBe(asrDuration);
      expect(llmPhase.duration).toBe(llmDuration);
    });
  });

  describe('finalizeRequest', () => {
    beforeEach(() => {
      metricsStore.initializeConversation(testConversationId, testAni);
      metricsStore.initializeRequest(testConversationId, testRequestId);
    });

    it('should update user input and AI reply', () => {
      metricsStore.finalizeRequest(testConversationId, testRequestId, testUserInput, testAiReply);

      const metrics = metricsStore.getConversationMetrics(testConversationId);
      assertMetrics(metrics);

      expect(metrics.requests[0].userInput).toBe(testUserInput);
      expect(metrics.requests[0].aiReply).toBe(testAiReply);
    });

    it('should handle empty user input and AI reply', () => {
      metricsStore.finalizeRequest(testConversationId, testRequestId, '', '');

      const metrics = metricsStore.getConversationMetrics(testConversationId);
      assertMetrics(metrics);

      expect(metrics.requests[0].userInput).toBe('');
      expect(metrics.requests[0].aiReply).toBe('');
    });
  });

  describe('finalizeConversation', () => {
    beforeEach(() => {
      metricsStore.initializeConversation(testConversationId, testAni);
    });

    it('should update the end time of the conversation', () => {
      metricsStore.finalizeConversation(testConversationId);

      const metrics = metricsStore.getConversationMetrics(testConversationId);
      assertMetrics(metrics);

      expect(metrics.endTime).toBeInstanceOf(Date);
    });
  });

  describe('getConversationMetrics', () => {
    it('should return null or undefined for non-existent conversation', () => {
      const metrics = metricsStore.getConversationMetrics('non-existent-id');
      expect(metrics == null).toBe(true); // Accept either null or undefined
    });
  });

  // Helper function to ensure metrics is not null
  function assertMetrics(metrics: any): asserts metrics is {
    conversationId: string;
    ani: string;
    requests: any[];
    phaseMetrics: any;
    endTime?: Date;
  } {
    expect(metrics).toBeDefined();
    expect(metrics).not.toBeNull();
  }

  describe('logInteractionMetrics and logConversationSummary', () => {
    beforeEach(() => {
      metricsStore.initializeConversation(testConversationId, testAni);
      metricsStore.initializeRequest(testConversationId, testRequestId);

      // Set up a simple phase
      metricsStore.startPhase(testConversationId, testRequestId, 'speechToText');
      metricsStore.endPhase(testConversationId, testRequestId, 'speechToText');

      metricsStore.finalizeRequest(testConversationId, testRequestId, testUserInput, testAiReply);
    });

    it('should not throw errors when logging interaction metrics', () => {
      expect(() => {
        metricsStore.logInteractionMetrics(testConversationId, testRequestId);
      }).not.toThrow();
    });

    it('should not throw errors when logging conversation summary', () => {
      expect(() => {
        metricsStore.logConversationSummary(testConversationId);
      }).not.toThrow();
    });
  });
});
