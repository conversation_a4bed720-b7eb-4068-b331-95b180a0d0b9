/**
 * EventMetricsCollector
 *
 * Collects metrics from events in the event-driven architecture.
 * This collector subscribes to events and tracks metrics for performance analysis.
 *
 * @module services/monitoring/event-metrics-collector
 */

import { EventEmitter } from '../events/event-emitter';
import {
  SessionEventType,
  TranscriptReceivedEventData,
  BotResponseReceivedEventData,
  PlaybackStartedEventData,
  PlaybackCompletedEventData,
  StateChangedEventData,
  BargeInDetectedEventData,
} from '../events/session-events';
import { logInfo, logDebug } from './logging';
import { SessionState } from '../../session/session-state-manager';

/**
 * Metrics for a single conversation turn
 */
interface TurnMetrics {
  /**
   * The turn ID
   */
  turnId: string;

  /**
   * The start time of the turn
   */
  startTime: number;

  /**
   * The end time of the turn
   */
  endTime?: number;

  /**
   * The duration of the speech-to-text phase in milliseconds
   */
  speechToTextDuration?: number;

  /**
   * The duration of the LLM processing phase in milliseconds
   */
  llmProcessingDuration?: number;

  /**
   * The duration of the text-to-speech phase in milliseconds
   */
  textToSpeechDuration?: number;

  /**
   * The duration of the playback phase in milliseconds
   */
  playbackDuration?: number;

  /**
   * The total duration of the turn in milliseconds
   */
  totalDuration?: number;

  /**
   * The user input text
   */
  userInput?: string;

  /**
   * The bot response text
   */
  botResponse?: string;

  /**
   * Whether the turn was interrupted by barge-in
   */
  wasInterrupted?: boolean;

  /**
   * The state transitions during the turn
   */
  stateTransitions: {
    from: SessionState;
    to: SessionState;
    timestamp: number;
  }[];
}

/**
 * Summary metrics for a session
 */
interface SessionMetrics {
  /**
   * The session ID
   */
  sessionId: string;

  /**
   * The start time of the session
   */
  startTime: number;

  /**
   * The end time of the session
   */
  endTime?: number;

  /**
   * The total duration of the session in milliseconds
   */
  totalDuration?: number;

  /**
   * The number of turns in the session
   */
  turnCount: number;

  /**
   * The average duration of speech-to-text phase in milliseconds
   */
  avgSpeechToTextDuration?: number;

  /**
   * The average duration of LLM processing phase in milliseconds
   */
  avgLlmProcessingDuration?: number;

  /**
   * The average duration of text-to-speech phase in milliseconds
   */
  avgTextToSpeechDuration?: number;

  /**
   * The average duration of playback phase in milliseconds
   */
  avgPlaybackDuration?: number;

  /**
   * The average total duration of turns in milliseconds
   */
  avgTurnDuration?: number;

  /**
   * The number of barge-ins
   */
  bargeInCount: number;

  /**
   * The state transition counts
   */
  stateTransitionCounts: Record<string, number>;
}

/**
 * Options for EventMetricsCollector
 */
export interface EventMetricsCollectorOptions {
  /**
   * The event emitter to subscribe to
   */
  eventEmitter: EventEmitter;

  /**
   * The session ID
   */
  sessionId: string;
}

/**
 * EventMetricsCollector class
 */
export class EventMetricsCollector {
  private eventEmitter: EventEmitter;
  private sessionId: string;
  private sessionMetrics: SessionMetrics;
  private currentTurn: TurnMetrics | null = null;
  private turns: TurnMetrics[] = [];
  private subscriptions: { unsubscribe: () => void }[] = [];

  /**
   * Constructor
   */
  constructor(options: EventMetricsCollectorOptions) {
    this.eventEmitter = options.eventEmitter;
    this.sessionId = options.sessionId;

    // Initialize session metrics
    this.sessionMetrics = {
      sessionId: this.sessionId,
      startTime: Date.now(),
      turnCount: 0,
      bargeInCount: 0,
      stateTransitionCounts: {},
    };

    // Subscribe to events
    this.subscribeToEvents();

    logInfo(`[EventMetricsCollector] Started collecting metrics for session ${this.sessionId}`);
  }

  /**
   * Subscribe to events
   */
  private subscribeToEvents(): void {
    // Subscribe to transcript received events
    this.subscriptions.push(
      this.eventEmitter.on(
        SessionEventType.TRANSCRIPT_RECEIVED,
        (data: TranscriptReceivedEventData) => {
          if (data.isFinal) {
            this.handleFinalTranscript(data);
          }
        }
      )
    );

    // Subscribe to bot response received events
    this.subscriptions.push(
      this.eventEmitter.on(
        SessionEventType.BOT_RESPONSE_RECEIVED,
        (data: BotResponseReceivedEventData) => {
          this.handleBotResponse(data);
        }
      )
    );

    // Subscribe to playback started events
    this.subscriptions.push(
      this.eventEmitter.on(SessionEventType.PLAYBACK_STARTED, (data: PlaybackStartedEventData) => {
        this.handlePlaybackStarted(data);
      })
    );

    // Subscribe to playback completed events
    this.subscriptions.push(
      this.eventEmitter.on(
        SessionEventType.PLAYBACK_COMPLETED,
        (data: PlaybackCompletedEventData) => {
          this.handlePlaybackCompleted(data);
        }
      )
    );

    // Subscribe to state changed events
    this.subscriptions.push(
      this.eventEmitter.on(SessionEventType.STATE_CHANGED, (data: StateChangedEventData) => {
        this.handleStateChanged(data);
      })
    );

    // Subscribe to barge-in detected events
    this.subscriptions.push(
      this.eventEmitter.on(SessionEventType.BARGE_IN_DETECTED, (data: BargeInDetectedEventData) => {
        this.handleBargeInDetected(data);
      })
    );

    // Subscribe to session closed events
    this.subscriptions.push(
      this.eventEmitter.on(SessionEventType.SESSION_CLOSED, () => {
        this.handleSessionClosed();
      })
    );
  }

  /**
   * Handle final transcript
   */
  private handleFinalTranscript(data: TranscriptReceivedEventData): void {
    // Start a new turn if one doesn't exist
    if (!this.currentTurn) {
      this.currentTurn = {
        turnId: `turn_${this.turns.length + 1}`,
        startTime: data.timestamp,
        stateTransitions: [],
      };
    }

    // Set the user input
    this.currentTurn.userInput = data.transcript.text;

    // Set the speech-to-text end time
    this.currentTurn.speechToTextDuration = data.timestamp - this.currentTurn.startTime;

    logDebug(
      `[EventMetricsCollector] Speech-to-text phase completed in ${this.currentTurn.speechToTextDuration}ms`
    );
  }

  /**
   * Handle bot response
   */
  private handleBotResponse(data: BotResponseReceivedEventData): void {
    // If no current turn, this is an initial greeting
    if (!this.currentTurn) {
      this.currentTurn = {
        turnId: `turn_${this.turns.length + 1}`,
        startTime: data.timestamp,
        stateTransitions: [],
      };
    }

    // Set the bot response
    this.currentTurn.botResponse = data.response.text;

    // Set the LLM processing duration
    if (this.currentTurn.speechToTextDuration) {
      this.currentTurn.llmProcessingDuration =
        data.timestamp - (this.currentTurn.startTime + this.currentTurn.speechToTextDuration);

      logDebug(
        `[EventMetricsCollector] LLM processing phase completed in ${this.currentTurn.llmProcessingDuration}ms`
      );
    }
  }

  /**
   * Handle playback started
   */
  private handlePlaybackStarted(data: PlaybackStartedEventData): void {
    // If no current turn, this is an initial greeting
    if (!this.currentTurn) {
      this.currentTurn = {
        turnId: `turn_${this.turns.length + 1}`,
        startTime: data.timestamp,
        stateTransitions: [],
      };
    }

    // Set the text-to-speech duration
    if (this.currentTurn.llmProcessingDuration) {
      this.currentTurn.textToSpeechDuration =
        data.timestamp -
        (this.currentTurn.startTime +
          this.currentTurn.speechToTextDuration! +
          this.currentTurn.llmProcessingDuration);

      logDebug(
        `[EventMetricsCollector] Text-to-speech phase completed in ${this.currentTurn.textToSpeechDuration}ms`
      );
    }
  }

  /**
   * Handle playback completed
   */
  private handlePlaybackCompleted(data: PlaybackCompletedEventData): void {
    // If no current turn, ignore
    if (!this.currentTurn) {
      return;
    }

    // Set the playback duration
    if (this.currentTurn.textToSpeechDuration) {
      this.currentTurn.playbackDuration =
        data.timestamp -
        (this.currentTurn.startTime +
          this.currentTurn.speechToTextDuration! +
          this.currentTurn.llmProcessingDuration! +
          this.currentTurn.textToSpeechDuration);

      logDebug(
        `[EventMetricsCollector] Playback phase completed in ${this.currentTurn.playbackDuration}ms`
      );
    }

    // Complete the turn
    this.completeTurn(data.timestamp);
  }

  /**
   * Handle state changed
   */
  private handleStateChanged(data: StateChangedEventData): void {
    // If no current turn, start a new one if this is a transition to PROCESSING_INPUT
    if (!this.currentTurn && data.newState === SessionState.PROCESSING_INPUT) {
      this.currentTurn = {
        turnId: `turn_${this.turns.length + 1}`,
        startTime: data.timestamp,
        stateTransitions: [],
      };
    }

    // Add the state transition to the current turn
    if (this.currentTurn) {
      this.currentTurn.stateTransitions.push({
        from: data.oldState,
        to: data.newState,
        timestamp: data.timestamp,
      });
    }

    // Update state transition counts
    const transitionKey = `${data.oldState} → ${data.newState}`;
    this.sessionMetrics.stateTransitionCounts[transitionKey] =
      (this.sessionMetrics.stateTransitionCounts[transitionKey] || 0) + 1;
  }

  /**
   * Handle barge-in detected
   */
  private handleBargeInDetected(data: BargeInDetectedEventData): void {
    // Increment barge-in count
    this.sessionMetrics.bargeInCount++;

    // If there's a current turn, mark it as interrupted
    if (this.currentTurn) {
      this.currentTurn.wasInterrupted = true;

      // Complete the turn
      this.completeTurn(data.timestamp);
    }
  }

  /**
   * Handle session closed
   */
  private handleSessionClosed(): void {
    // Complete the current turn if there is one
    if (this.currentTurn) {
      this.completeTurn(Date.now());
    }

    // Complete the session metrics
    this.completeSessionMetrics();

    // Unsubscribe from all events
    this.unsubscribeFromEvents();

    // Log the session metrics
    this.logSessionMetrics();
  }

  /**
   * Complete the current turn
   */
  private completeTurn(timestamp: number): void {
    if (!this.currentTurn) {
      return;
    }

    // Set the end time
    this.currentTurn.endTime = timestamp;

    // Calculate the total duration
    this.currentTurn.totalDuration = this.currentTurn.endTime - this.currentTurn.startTime;

    // Add the turn to the list
    this.turns.push(this.currentTurn);

    // Increment the turn count
    this.sessionMetrics.turnCount++;

    // Log the turn metrics
    this.logTurnMetrics(this.currentTurn);

    // Reset the current turn
    this.currentTurn = null;
  }

  /**
   * Complete the session metrics
   */
  private completeSessionMetrics(): void {
    // Set the end time
    this.sessionMetrics.endTime = Date.now();

    // Calculate the total duration
    this.sessionMetrics.totalDuration = this.sessionMetrics.endTime - this.sessionMetrics.startTime;

    // Calculate average durations
    if (this.turns.length > 0) {
      let totalSpeechToTextDuration = 0;
      let totalLlmProcessingDuration = 0;
      let totalTextToSpeechDuration = 0;
      let totalPlaybackDuration = 0;
      let totalTurnDuration = 0;
      let speechToTextCount = 0;
      let llmProcessingCount = 0;
      let textToSpeechCount = 0;
      let playbackCount = 0;

      for (const turn of this.turns) {
        if (turn.speechToTextDuration) {
          totalSpeechToTextDuration += turn.speechToTextDuration;
          speechToTextCount++;
        }

        if (turn.llmProcessingDuration) {
          totalLlmProcessingDuration += turn.llmProcessingDuration;
          llmProcessingCount++;
        }

        if (turn.textToSpeechDuration) {
          totalTextToSpeechDuration += turn.textToSpeechDuration;
          textToSpeechCount++;
        }

        if (turn.playbackDuration) {
          totalPlaybackDuration += turn.playbackDuration;
          playbackCount++;
        }

        if (turn.totalDuration) {
          totalTurnDuration += turn.totalDuration;
        }
      }

      this.sessionMetrics.avgSpeechToTextDuration =
        speechToTextCount > 0 ? totalSpeechToTextDuration / speechToTextCount : undefined;
      this.sessionMetrics.avgLlmProcessingDuration =
        llmProcessingCount > 0 ? totalLlmProcessingDuration / llmProcessingCount : undefined;
      this.sessionMetrics.avgTextToSpeechDuration =
        textToSpeechCount > 0 ? totalTextToSpeechDuration / textToSpeechCount : undefined;
      this.sessionMetrics.avgPlaybackDuration =
        playbackCount > 0 ? totalPlaybackDuration / playbackCount : undefined;
      this.sessionMetrics.avgTurnDuration = totalTurnDuration / this.turns.length;
    }
  }

  /**
   * Unsubscribe from all events
   */
  private unsubscribeFromEvents(): void {
    for (const subscription of this.subscriptions) {
      subscription.unsubscribe();
    }

    this.subscriptions = [];
  }

  /**
   * Log turn metrics
   */
  private logTurnMetrics(turn: TurnMetrics): void {
    logInfo(`[EventMetricsCollector] Turn ${turn.turnId} completed:`);
    logInfo(`  User: ${turn.userInput || '(none)'}`);
    logInfo(`  Bot: ${turn.botResponse || '(none)'}`);
    logInfo(`  Speech-to-Text: ${turn.speechToTextDuration || 'N/A'}ms`);
    logInfo(`  LLM Processing: ${turn.llmProcessingDuration || 'N/A'}ms`);
    logInfo(`  Text-to-Speech: ${turn.textToSpeechDuration || 'N/A'}ms`);
    logInfo(`  Playback: ${turn.playbackDuration || 'N/A'}ms`);
    logInfo(`  Total Duration: ${turn.totalDuration || 'N/A'}ms`);
    logInfo(`  Interrupted: ${turn.wasInterrupted ? 'Yes' : 'No'}`);
  }

  /**
   * Log session metrics
   */
  private logSessionMetrics(): void {
    logInfo(`[EventMetricsCollector] Session ${this.sessionId} metrics:`);
    logInfo(`  Total Duration: ${this.sessionMetrics.totalDuration || 'N/A'}ms`);
    logInfo(`  Turn Count: ${this.sessionMetrics.turnCount}`);
    logInfo(`  Barge-In Count: ${this.sessionMetrics.bargeInCount}`);
    logInfo(`  Avg Speech-to-Text: ${this.sessionMetrics.avgSpeechToTextDuration || 'N/A'}ms`);
    logInfo(`  Avg LLM Processing: ${this.sessionMetrics.avgLlmProcessingDuration || 'N/A'}ms`);
    logInfo(`  Avg Text-to-Speech: ${this.sessionMetrics.avgTextToSpeechDuration || 'N/A'}ms`);
    logInfo(`  Avg Playback: ${this.sessionMetrics.avgPlaybackDuration || 'N/A'}ms`);
    logInfo(`  Avg Turn Duration: ${this.sessionMetrics.avgTurnDuration || 'N/A'}ms`);

    // Log state transition counts
    logInfo('  State Transitions:');
    for (const [transition, count] of Object.entries(this.sessionMetrics.stateTransitionCounts)) {
      logInfo(`    ${transition}: ${count}`);
    }
  }

  /**
   * Get the session metrics
   */
  public getSessionMetrics(): SessionMetrics {
    return { ...this.sessionMetrics };
  }

  /**
   * Get the turn metrics
   */
  public getTurnMetrics(): TurnMetrics[] {
    return [...this.turns];
  }
}
