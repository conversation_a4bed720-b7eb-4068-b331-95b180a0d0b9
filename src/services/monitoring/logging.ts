/**
 * Log flooding protection: suppress repeated identical messages per log level.
 * When a message is repeated, only the first is logged; repeats are counted and a summary is logged when a new message arrives or on flush.
 */
type LogLevelKey = 'DEBUG' | 'INFO' | 'METRICS' | 'WARN' | 'ERROR';

interface LogFloodState {
  lastMessage?: string;
  repeatCount: number;
}

const logFloodStates: Record<LogLevelKey, LogFloodState> = {
  DEBUG: { lastMessage: undefined, repeatCount: 0 },
  INFO: { lastMessage: undefined, repeatCount: 0 },
  METRICS: { lastMessage: undefined, repeatCount: 0 },
  WARN: { lastMessage: undefined, repeatCount: 0 },
  ERROR: { lastMessage: undefined, repeatCount: 0 },
};

/**
 * Flush any suppressed repeated log summaries for all log levels.
 * Call this on shutdown to ensure all suppressed counts are reported.
 */
export function flushSuppressedLogs() {
  (Object.keys(logFloodStates) as Log<PERSON><PERSON><PERSON><PERSON><PERSON>[]).forEach(level => {
    const state = logFloodStates[level];
    if (state.repeatCount > 0 && state.lastMessage !== undefined) {
      logSuppressedSummary(level, state.repeatCount, state.lastMessage);
      state.repeatCount = 0;
      state.lastMessage = undefined;
    }
  });
}

/**
 * Log a summary message for suppressed repeats.
 */
function logSuppressedSummary(level: LogLevelKey, count: number, message: string) {
  const timestamp = getTimestamp();
  const summary = `Previous message repeated ${count} times: "${truncateString(message, 80)}"`;
  switch (level) {
    case 'DEBUG':
      if (currentLogLevel <= LOG_LEVEL.DEBUG) {
        console.log(`${colors.dim}[DEBUG ${timestamp}] ${summary}${colors.reset}`);
      }
      break;
    case 'INFO':
      if (currentLogLevel <= LOG_LEVEL.INFO) {
        console.log(`${colors.cyan}[INFO ${timestamp}] ${summary}${colors.reset}`);
      }
      break;
    case 'METRICS':
      if (currentLogLevel <= LOG_LEVEL.METRICS) {
        console.log(
          `${colors.green}${colors.bright}[METRICS ${timestamp}] ${summary}${colors.reset}`
        );
      }
      break;
    case 'WARN':
      if (currentLogLevel <= LOG_LEVEL.WARN) {
        console.warn(
          `${colors.yellow}${colors.bright}[WARN ${timestamp}] ${summary}${colors.reset}`
        );
      }
      break;
    case 'ERROR':
      // Always log error summaries
      console.error(`${colors.red}${colors.bright}[ERROR ${timestamp}] ${summary}${colors.reset}`);
      break;
  }
}

// ANSI color codes for terminal output
export const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  purple: '\x1b[38;5;135m', // A distinct purple color for state changes
  orange: '\x1b[38;5;208m', // Orange color
  bg_blue: '\x1b[44m', // Blue background
  bg_green: '\x1b[42m', // Green background
  bg_purple: '\x1b[45m', // Purple background
};

// Log levels
export const LOG_LEVEL = {
  DEBUG: 0,
  INFO: 1,
  METRICS: 2,
  WARN: 3,
  ERROR: 4,
};

// Current log level - can be set via environment variable
export const currentLogLevel = process.env.LOG_LEVEL
  ? process.env.LOG_LEVEL === 'DEBUG'
    ? LOG_LEVEL.DEBUG
    : process.env.LOG_LEVEL === 'INFO'
    ? LOG_LEVEL.INFO
    : process.env.LOG_LEVEL === 'WARN'
    ? LOG_LEVEL.WARN
    : process.env.LOG_LEVEL === 'ERROR'
    ? LOG_LEVEL.ERROR
    : LOG_LEVEL.METRICS
  : process.env.METRICS_LOG_LEVEL
  ? parseInt(process.env.METRICS_LOG_LEVEL)
  : LOG_LEVEL.METRICS;

// Category-specific log flags
export const shouldLogHttpRequests = process.env.LOG_HTTP_REQUESTS === 'true';
export const shouldLogHttpResponses = process.env.LOG_HTTP_RESPONSES === 'true';
export const shouldLogOpenAIRequests = process.env.LOG_OPENAI_REQUESTS === 'true';
export const shouldLogOpenAIResponses = process.env.LOG_OPENAI_RESPONSES === 'true';
export const shouldLogWebSocketMessages = process.env.LOG_WEBSOCKET_MESSAGES === 'true';
export const shouldLogASRInterim = process.env.LOG_ASR_INTERIM === 'true';

/**
 * Get current timestamp in HH:MM:SS format
 */
export function getTimestamp(): string {
  return new Date().toISOString().substring(11, 19); // Extract HH:MM:SS
}

/**
 * Log debug information (only visible at DEBUG level)
 */
export function logDebug(message: string): void {
  const state = logFloodStates.DEBUG;
  if (message === state.lastMessage) {
    state.repeatCount++;
    return;
  }
  if (state.repeatCount > 0 && state.lastMessage !== undefined) {
    logSuppressedSummary('DEBUG', state.repeatCount, state.lastMessage);
  }
  state.lastMessage = message;
  state.repeatCount = 0;
  if (currentLogLevel <= LOG_LEVEL.DEBUG) {
    console.log(`${colors.dim}[DEBUG ${getTimestamp()}] ${message}${colors.reset}`);
  }
}

/**
 * Log general information (visible at INFO level and below)
 */
export function logInfo(message: string): void {
  const state = logFloodStates.INFO;
  if (message === state.lastMessage) {
    state.repeatCount++;
    return;
  }
  if (state.repeatCount > 0 && state.lastMessage !== undefined) {
    logSuppressedSummary('INFO', state.repeatCount, state.lastMessage);
  }
  state.lastMessage = message;
  state.repeatCount = 0;
  if (currentLogLevel <= LOG_LEVEL.INFO) {
    console.log(`${colors.cyan}[INFO ${getTimestamp()}] ${message}${colors.reset}`);
  }
}

/**
 * Log metrics data (visible at METRICS level and below)
 */
export function logMetrics(message: string): void {
  const state = logFloodStates.METRICS;
  if (message === state.lastMessage) {
    state.repeatCount++;
    return;
  }
  if (state.repeatCount > 0 && state.lastMessage !== undefined) {
    logSuppressedSummary('METRICS', state.repeatCount, state.lastMessage);
  }
  state.lastMessage = message;
  state.repeatCount = 0;
  if (currentLogLevel <= LOG_LEVEL.METRICS) {
    console.log(
      `${colors.green}${colors.bright}[METRICS ${getTimestamp()}] ${message}${colors.reset}`
    );
  }
}

/**
 * Log warning messages (visible at WARN level and below)
 */
export function logWarning(message: string): void {
  const state = logFloodStates.WARN;
  if (message === state.lastMessage) {
    state.repeatCount++;
    return;
  }
  if (state.repeatCount > 0 && state.lastMessage !== undefined) {
    logSuppressedSummary('WARN', state.repeatCount, state.lastMessage);
  }
  state.lastMessage = message;
  state.repeatCount = 0;
  if (currentLogLevel <= LOG_LEVEL.WARN) {
    console.warn(
      `${colors.yellow}${colors.bright}[WARN ${getTimestamp()}] ${message}${colors.reset}`
    );
  }
}

/**
 * Log error messages (always visible)
 */
export function logError(message: string): void {
  const state = logFloodStates.ERROR;
  if (message === state.lastMessage) {
    state.repeatCount++;
    return;
  }
  if (state.repeatCount > 0 && state.lastMessage !== undefined) {
    logSuppressedSummary('ERROR', state.repeatCount, state.lastMessage);
  }
  state.lastMessage = message;
  state.repeatCount = 0;
  // Always log errors
  console.error(`${colors.red}${colors.bright}[ERROR ${getTimestamp()}] ${message}${colors.reset}`);
}

/**
 * Truncate a string if it's too long
 */
export function truncateString(str: string, maxLength = 30): string {
  return str.length > maxLength ? str.substring(0, maxLength - 3) + '...' : str;
}

/**
 * Format a system prompt for logging (truncated)
 */
export function formatSystemPrompt(prompt: string): string {
  const lines = prompt.split('\n');
  const firstLine = lines[0] || '';
  return `${truncateString(firstLine, 50)} (${prompt.length} chars)`;
}

/**
 * Log HTTP request information (only if LOG_HTTP_REQUESTS is enabled)
 */
export function logHttpRequest(message: string): void {
  if (shouldLogHttpRequests) {
    logInfo(`[HTTP] ${message}`);
  }
}

/**
 * Log HTTP response information (only if LOG_HTTP_RESPONSES is enabled)
 */
export function logHttpResponse(message: string): void {
  if (shouldLogHttpResponses) {
    logInfo(`[HTTP] ${message}`);
  }
}

/**
 * Log OpenAI request information (only if LOG_OPENAI_REQUESTS is enabled)
 */
export function logOpenAIRequest(message: string): void {
  if (shouldLogOpenAIRequests) {
    logInfo(`[OpenAI] ${message}`);
  }
}

/**
 * Log OpenAI response information (only if LOG_OPENAI_RESPONSES is enabled)
 */
export function logOpenAIResponse(message: string): void {
  if (shouldLogOpenAIResponses) {
    logInfo(`[OpenAI] ${message}`);
  }
}

/**
 * Log WebSocket message information (only if LOG_WEBSOCKET_MESSAGES is enabled)
 */
export function logWebSocketMessage(message: string): void {
  if (shouldLogWebSocketMessages) {
    logInfo(`[WebSocket] ${message}`);
  }
}

/**
 * Log ASR interim transcript information (only if LOG_ASR_INTERIM is enabled)
 */
export function logASRInterim(message: string): void {
  if (shouldLogASRInterim) {
    logInfo(`[ASR] ${message}`);
  }
}

/**
 * Prepare metadata for logging by excluding binary data
 * @param metadata The original metadata object
 * @returns A new metadata object with binary data excluded
 */
/**
 * Prepares metadata for logging by including only important fields and summarizing large/binary data.
 * - Only whitelisted fields are logged.
 * - Large/binary fields are replaced with a summary string.
 * - Prevents accidental logging of large or sensitive data.
 */
export function prepareMetadataForLogging(
  metadata?: Record<string, unknown>
): Record<string, unknown> | undefined {
  if (!metadata) {
    return undefined;
  }

  // Define a whitelist of important fields to log
  const whitelist = [
    'reason',
    'inputType',
    'transcript',
    'bargeInSource',
    'bargeInText',
    'state',
    'previousState',
    'dtmf',
    'error',
    'ttsProvider',
    'asrProvider',
    'metrics',
    // Add more fields as needed
  ];

  const result: Record<string, unknown> = {};

  // First, check for any audio-related fields and exclude them
  for (const key in metadata) {
    // Skip any audio-related fields or large binary data
    if (
      key === 'audioBytes' ||
      key === 'audio' ||
      key === 'audioData' ||
      key === 'botResponse' || // botResponse may contain audio data
      key.includes('audio') ||
      key.includes('bytes')
    ) {
      const value = metadata[key];
      const size =
        value && typeof value === 'object' && 'length' in value
          ? (value as { length: number }).length
          : typeof value === 'string'
          ? value.length
          : 0;

      // Only add a summary, not the actual data
      if (size > 0) {
        result[key] = `[Binary data: ${size} bytes]`;
      }

      // Remove this field from metadata to prevent it from being processed again
      delete metadata[key];
    }
  }

  // Now process the whitelisted fields
  for (const key of whitelist) {
    if (key in metadata) {
      result[key] = metadata[key];
    }
  }

  return Object.keys(result).length > 0 ? result : undefined;
}

/**
 * Log state transitions with a distinctive color
 * @param oldState The previous state
 * @param newState The new state
 * @param metadata Optional metadata about the transition
 */
export function logStateTransition(
  oldState: string,
  newState: string,
  source = 'StateManager',
  metadata?: Record<string, unknown>
): void {
  const loggableMetadata = prepareMetadataForLogging(metadata);
  const metadataStr = loggableMetadata ? ` (${JSON.stringify(loggableMetadata)})` : '';
  const timestamp = getTimestamp();

  // Use a much brighter color scheme for better readability
  console.log(
    `${colors.bg_blue}${colors.bright}[STATE ${timestamp}]${colors.reset} ${colors.bright}${colors.cyan}${source}: ${colors.bright}${colors.green}${oldState} ${colors.bright}${colors.yellow}→ ${colors.bright}${colors.green}${newState}${colors.reset} ${colors.bright}${colors.yellow}${metadataStr}${colors.reset}`
  );
}
