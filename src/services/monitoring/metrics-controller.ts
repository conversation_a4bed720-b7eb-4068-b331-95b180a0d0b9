import { Router, Request, Response } from 'express';
import { PerformanceMetricsStore } from './performance-metrics-store';
import { TrendAnalyzer } from './trend-analyzer';
import { SystemMetricsCollector } from './system-metrics-collector';
import { TrendMetrics, SystemMetrics } from '../../types/monitoring';

export const metricsRouter = (): Router => {
  const router = Router();
  const metricsStore = PerformanceMetricsStore.getInstance();
  const trendAnalyzer = TrendAnalyzer.getInstance();
  const systemMetricsCollector = SystemMetricsCollector.getInstance();

  /**
   * Get metrics for a specific conversation
   * GET /metrics/conversation/:conversationId
   */
  router.get('/conversation/:conversationId', (req: Request, res: Response) => {
    try {
      const conversationId = req.params.conversationId;
      const metrics = metricsStore.getConversationMetrics(conversationId);

      if (!metrics) {
        return res.status(404).json({
          error: `No metrics found for conversation ${conversationId}`,
        });
      }

      res.json(metrics);
    } catch (error) {
      console.error('[Metrics] Error fetching conversation metrics:', error);
      res.status(500).json({
        error: 'Internal server error fetching conversation metrics',
      });
    }
  });

  /**
   * Get system metrics
   * GET /metrics/system
   * Query params:
   * - duration: time range in milliseconds (optional)
   */
  router.get('/system', (req: Request, res: Response) => {
    try {
      const duration = req.query.duration ? parseInt(req.query.duration as string) : undefined;
      let metrics: SystemMetrics | SystemMetrics[] | null;

      if (duration) {
        metrics = systemMetricsCollector.getMetricsHistory(duration);
      } else {
        metrics = systemMetricsCollector.getLatestMetrics();
      }

      if (!metrics) {
        return res.status(404).json({
          error: 'No system metrics available',
        });
      }

      res.json(metrics);
    } catch (error) {
      console.error('[Metrics] Error fetching system metrics:', error);
      res.status(500).json({
        error: 'Internal server error fetching system metrics',
      });
    }
  });

  /**
   * Get performance trends
   * GET /metrics/trends
   * Query params:
   * - timeRange: "1h" | "24h" | "7d" (optional, defaults to all)
   */
  router.get('/trends', (req: Request, res: Response) => {
    try {
      const timeRange = req.query.timeRange as string;
      let trends: TrendMetrics | null | Record<string, TrendMetrics>;

      if (timeRange) {
        trends = trendAnalyzer.getTrends(timeRange);
        if (!trends) {
          return res.status(404).json({
            error: `No trends found for time range ${timeRange}`,
          });
        }
      } else {
        const trendMap = trendAnalyzer.getAllTrends();
        if (trendMap.size === 0) {
          return res.status(404).json({
            error: 'No trends available',
          });
        }
        // Convert Map to object for JSON serialization
        trends = Object.fromEntries(trendMap);
      }

      res.json(trends);
    } catch (error) {
      console.error('[Metrics] Error fetching trends:', error);
      res.status(500).json({
        error: 'Internal server error fetching trends',
      });
    }
  });

  return router;
};
