# Monitoring Service: Technical Overview

## Purpose

The Monitoring Service provides comprehensive logging, metrics tracking, and performance monitoring for the application. It tracks request phases, session metrics, and provides a structured logging system with different log levels and formatting.

---

## Key Responsibilities

- Track request phases and timing metrics
- Log session events and metrics
- Provide structured logging with different levels
- Adapt session metrics to the monitoring infrastructure
- Generate performance reports and summaries

---

## Core Components and Their Roles

### PerformanceLogger (`src/services/monitoring/performance-logger.ts`)

- Singleton service for tracking performance metrics
- Creates and manages request contexts
- Tracks phase timing and metrics
- Generates performance reports

### RequestContext

- Represents a single user request
- Tracks phases within the request
- Stores metadata like user input and AI reply
- Calculates timing metrics

### PhaseContext

- Represents a specific phase within a request
- Tracks start and end times
- Calculates duration

### SessionMetricsAdapter (`src/services/monitoring/session-metrics-adapter.ts`)

- Adapts the Session class to the metrics infrastructure
- Encapsulates metrics-related functionality
- Provides a clean interface for session metrics

### Logging System (`src/services/monitoring/logging.ts`)

- Provides structured logging with different levels
- Supports color-coded output
- Configurable verbosity and formatting
- Special handling for metrics logs

#### Adapter Simplification Notice

As of 2025-05, all session metrics tracking is handled exclusively by `SessionMetricsAdapter`. The previous `SessionMetricsTracker` implementation has been removed to eliminate redundancy and confusion. All session-level metrics should use `SessionMetricsAdapter` as the single, canonical adapter.
---

## Request Phases

The Monitoring Service tracks three key phases in the request processing pipeline:

1. **Speech to Text**: Time from receiving audio data to completing speech-to-text (ASR processing)
2. **LLM Processing**: Time from having the transcript to getting the LLM response
3. **Text to Speech**: Time from having the LLM text response to completing the audio response delivery

These phases help identify bottlenecks in the processing pipeline.

> **Note:** For important limitations, edge cases, and best practices in phase metrics, see [performance-metrics.md](./performance-metrics.md#limitations-edge-cases-and-enforcement).
---

## Log Levels

The logging system supports the following log levels:

- **DEBUG**: Detailed debugging information
- **INFO**: General information about application operation
- **METRICS**: Performance metrics and timing information
- **WARNING**: Potential issues that don't prevent operation
- **ERROR**: Errors that prevent normal operation

---

## Component Interactions

1. **Request Creation**: PerformanceLogger creates a RequestContext for a new user request
2. **Phase Tracking**: RequestContext tracks phases as they start and end
3. **Metrics Collection**: RequestContext collects metrics about the request
4. **Logging**: Logging functions output information at appropriate levels
5. **Report Generation**: PerformanceLogger generates reports based on collected metrics

---

## Configuration Options

The Monitoring Service can be configured through environment variables:

- `LOG_LEVEL`: Minimum log level to display (debug, info, metrics, warning, error)
- `ENABLE_COLORED_LOGS`: Enable color-coded log output (true/false)
- `LOG_OPENAI_REQUESTS`: Log OpenAI API requests (true/false)
- `LOG_DUPLICATE_CHECKS`: Log detailed transcript duplicate checks (true/false)

---

## Design Principles

- **Singleton Pattern**: Single instance for global access to performance logging
- **Adapter Pattern**: Clean interface for session metrics
- **Structured Logging**: Consistent format and levels for logs
- **Phase-Based Metrics**: Clear separation of processing phases
- **Minimal Overhead**: Efficient metrics collection with minimal impact

---

## File Structure

- `src/services/monitoring/`
  - `performance-logger.ts` – Core metrics tracking
  - `session-metrics-adapter.ts` – Adapter for session metrics
  - `logging.ts` – Structured logging system
  - `__tests__/` – Unit tests for monitoring functionality

---

## Usage Example

```typescript
// Get the PerformanceLogger instance
const logger = PerformanceLogger.getInstance();

// Create a request context
const context = logger.createRequest();

// Set user input
context.setUserInput('Hello, how are you?');

// Track phases
const speechToTextPhase = context.trackPhase('speechToText');
// ... speech-to-text processing ...
speechToTextPhase.end();

const llmPhase = context.trackPhase('llmProcessing');
// ... LLM processing ...
llmPhase.end();

const ttsPhase = context.trackPhase('textToSpeech');
// ... text-to-speech processing ...
ttsPhase.end();

// Set AI reply
context.setAiReply('I am doing well, thank you for asking!');

// Finalize the request
context.finalize();

// Log at different levels
logDebug('Detailed debugging information');
logInfo('General information about operation');
logMetrics('Performance metrics: 150ms');
logWarning('Potential issue detected');
logError('Error occurred during processing');
```

---

## Integration with Other Services

The Monitoring Service integrates with several other services:

- **Session**: Uses SessionMetricsAdapter for metrics tracking
- **BotServiceAdapter**: Tracks LLM processing phase
- **AudioServiceAdapter**: Tracks speech-to-text and text-to-speech phases
- **All Services**: Use the logging system for consistent logging
