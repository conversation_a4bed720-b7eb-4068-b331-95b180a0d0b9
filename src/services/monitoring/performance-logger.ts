import { PerformanceMetricsStore } from './performance-metrics-store';
import { MetricsPhase } from '../../types/monitoring';
import { logInfo, logWarning } from './logging';

/**
 * Context class for automatic phase management
 * Automatically starts a phase when created and ends it when end() is called
 */
export class PhaseContext {
  constructor(private logger: PerformanceLogger, private phase: MetricsPhase) {
    this.logger.startPhase(phase);
  }

  /**
   * End the phase and calculate duration
   */
  end(): void {
    this.logger.endPhase(this.phase);
  }
}

/**
 * Context class for managing a request
 * Provides a fluent API for tracking phases and setting user input and AI reply
 */
export class RequestContext {
  constructor(private logger: PerformanceLogger, public readonly requestId: string) {}

  /**
   * Set the user input for this request
   * @param userInput The user input text
   * @returns This RequestContext for chaining
   */
  setUserInput(userInput: string): this {
    this.logger.setCurrentUserInput(userInput);
    return this;
  }

  /**
   * Set the AI reply for this request
   * @param aiReply The AI reply text
   * @returns This RequestContext for chaining
   */
  setAiReply(aiReply: string): this {
    this.logger.setCurrentAiReply(aiReply);
    return this;
  }

  /**
   * Create a context for tracking a phase
   * @param phase The phase to track
   * @returns A PhaseContext that will automatically end the phase when end() is called
   */
  trackPhase(phase: MetricsPhase): PhaseContext {
    return this.logger.trackPhase(phase);
  }

  /**
   * Transition from one phase to another
   * @param from The phase to end
   * @param to The phase to start
   * @returns A PhaseContext for the new phase
   */
  transitionPhase(from: MetricsPhase, to: MetricsPhase): PhaseContext {
    return this.logger.transitionPhase(from, to);
  }

  /**
   * Finalize the request with optional AI reply
   * @param aiReply Optional AI reply to set before finalizing
   */
  finalize(aiReply = ''): void {
    if (aiReply) {
      this.logger.setCurrentAiReply(aiReply);
    }
    // Pass the current values explicitly to ensure they're used
    const userInput = this.logger.getCurrentUserInput();
    const finalAiReply = aiReply || this.logger.getCurrentAiReply();
    this.logger.finalizeRequest(userInput, finalAiReply);
  }
}

/**
 * Logs performance metrics for the application
 */
export class PerformanceLogger {
  private static instance: PerformanceLogger;
  private metricsStore: PerformanceMetricsStore;
  private currentConversationId: string | null = null;
  private currentRequestId: string | null = null;
  private currentUserInput = '';
  private currentAiReply = '';

  private constructor() {
    this.metricsStore = PerformanceMetricsStore.getInstance();
  }

  public static getInstance(): PerformanceLogger {
    if (!PerformanceLogger.instance) {
      PerformanceLogger.instance = new PerformanceLogger();
    }
    return PerformanceLogger.instance;
  }

  /**
   * Set the current conversation ID
   */
  public setConversationId(conversationId: string | null) {
    this.currentConversationId = conversationId;

    // Initialize conversation metrics if needed
    if (
      conversationId &&
      this.isEnabled() &&
      !this.metricsStore.getConversationMetrics(conversationId)
    ) {
      this.metricsStore.initializeConversation(conversationId, 'unknown');
    }
  }

  /**
   * Get the current request ID
   */
  public getCurrentRequestId(): string | null {
    return this.currentRequestId;
  }

  /**
   * Set the current user input for metrics
   */
  public setCurrentUserInput(userInput: string): void {
    this.currentUserInput = userInput;

    // Also set it directly on the current request in the metrics store
    if (this.isEnabled() && this.currentConversationId && this.currentRequestId) {
      this.metricsStore.setUserInput(this.currentConversationId, this.currentRequestId, userInput);
    }
  }

  /**
   * Get the current user input
   */
  public getCurrentUserInput(): string {
    return this.currentUserInput;
  }

  /**
   * Set the current AI reply for metrics
   */
  public setCurrentAiReply(aiReply: string): void {
    this.currentAiReply = aiReply;

    // Also set it directly on the current request in the metrics store
    if (this.isEnabled() && this.currentConversationId && this.currentRequestId) {
      // Pass the current user input to help find the right request
      this.metricsStore.setAiReply(
        this.currentConversationId,
        this.currentRequestId,
        aiReply,
        this.currentUserInput
      );
    }
  }

  /**
   * Get the current AI reply
   */
  public getCurrentAiReply(): string {
    return this.currentAiReply;
  }

  /**
   * Generate a unique request ID
   */
  public generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;
  }

  /**
   * Initialize a new request
   */
  public initializeRequest(userInput?: string): string {
    if (!this.isEnabled() || !this.currentConversationId) {
      return '';
    }

    const requestId = this.generateRequestId();
    this.currentRequestId = requestId;

    // If userInput is provided, use it; otherwise use the stored value
    const initialUserInput = userInput || this.currentUserInput;

    // Initialize the request with the user input
    this.metricsStore.initializeRequest(
      this.currentConversationId,
      requestId,
      initialUserInput || ''
    );

    return requestId;
  }

  /**
   * Create a new request context
   * @param userInput Optional user input to initialize the request with
   * @returns A RequestContext for managing the request
   *
   * @example
   * ```
   * const request = performanceLogger.createRequest('Hello');
   *
   * const asr = request.trackPhase('speechToText');
   * try {
   *   // ASR processing...
   * } finally {
   *   asr.end();
   * }
   *
   * request.finalize('AI response');
   * ```
   */
  public createRequest(userInput?: string): RequestContext {
    const requestId = this.initializeRequest(userInput);
    const requestContext = new RequestContext(this, requestId);

    // If user input was provided, set it on the request context
    if (userInput) {
      requestContext.setUserInput(userInput);
    }

    return requestContext;
  }

  /**
   * Start timing for a specific phase
   */
  public startPhase(phase: MetricsPhase) {
    if (!this.isEnabled()) {
      logInfo(`[PerformanceLogger] Metrics are disabled. Not starting phase: ${phase}`);
      return;
    }
    if (!this.currentConversationId) {
      logWarning(`[PerformanceLogger] No conversationId set. Not starting phase: ${phase}`);
      return;
    }
    if (!this.currentRequestId) {
      throw new Error(
        `[PerformanceLogger] startPhase called without a currentRequestId! All metrics phases must be associated with a requestId.`
      );
    }

    this.metricsStore.startPhase(this.currentConversationId, this.currentRequestId, phase);
  }

  /**
   * End timing for a specific phase
   */
  public endPhase(phase: MetricsPhase) {
    if (!this.isEnabled() || !this.currentConversationId || !this.currentRequestId) {
      return;
    }

    this.metricsStore.endPhase(this.currentConversationId, this.currentRequestId, phase);
  }

  /**
   * Create a context for tracking a phase
   * @param phase The phase to track
   * @returns A PhaseContext that will automatically end the phase when end() is called
   *
   * @example
   * ```
   * const asr = performanceLogger.trackPhase('speechToText');
   * try {
   *   // ASR processing...
   * } finally {
   *   asr.end(); // Always ends the phase, even if an error occurs
   * }
   * ```
   */
  public trackPhase(phase: MetricsPhase): PhaseContext {
    return new PhaseContext(this, phase);
  }

  /**
   * Transition from one phase to another
   * @param from The phase to end
   * @param to The phase to start
   * @returns A PhaseContext for the new phase
   *
   * @example
   * ```
   * const llm = performanceLogger.transitionPhase('speechToText', 'llmProcessing');
   * try {
   *   // LLM processing...
   * } finally {
   *   llm.end();
   * }
   * ```
   */
  public transitionPhase(from: MetricsPhase, to: MetricsPhase): PhaseContext {
    this.endPhase(from);
    return this.trackPhase(to);
  }

  /**
   * Finalize a request with user input and AI reply
   * @param userInput The user's input text (optional, will use stored input if not provided)
   * @param aiReply The AI's response text (optional, will use stored reply if not provided)
   */
  public finalizeRequest(userInput = '', aiReply = '') {
    if (!this.isEnabled() || !this.currentConversationId || !this.currentRequestId) {
      return;
    }

    // Use the stored values if none are provided
    const finalUserInput = userInput || this.currentUserInput;
    const finalAiReply = aiReply || this.currentAiReply;

    // No need to log every request finalization - this happens frequently

    this.metricsStore.finalizeRequest(
      this.currentConversationId,
      this.currentRequestId,
      finalUserInput,
      finalAiReply
    );

    // Reset the request ID but keep the user input and AI reply for potential reuse
    this.currentRequestId = null;
    // Don't clear these values as they might be needed for the next request
    // this.currentUserInput = '';
    // this.currentAiReply = '';
  }

  /**
   * Finalize conversation metrics
   */
  public finalizeConversation() {
    if (!this.isEnabled() || !this.currentConversationId) {
      return;
    }

    this.metricsStore.finalizeConversation(this.currentConversationId);

    // Reset the conversation ID
    this.currentConversationId = null;
  }

  /**
   * Check if metrics are enabled
   */
  public isEnabled(): boolean {
    return process.env.ENABLE_PERF_MONITOR?.toLowerCase() === 'true';
  }
  /**
   * Get phase durations for a request (for debugging/logging)
   */
  public getPhaseDurations(
    conversationId: string,
    requestId: string
  ): { [phase: string]: number | null } | null {
    // Debug helper: returns phase durations (speechToText, llmProcessing, textToSpeech) or null.
    // Accesses private fields; for internal logging only, not for production/business logic.
    // (If you need more, refactor as needed)
    // (melme)
    // ---
    // Implementation:
    // - getRequest is private, but accessible here
    // - phases are always present, but duration may be null
    // - Return a simple object
    // ---
    // (end doc)
    // ---
    // Implementation:
    // @ts-ignore (access private for debug)
    const req = this.metricsStore.getRequest(conversationId, requestId);
    if (!req) {
      return null;
    }
    return {
      speechToText: req.phases.speechToText?.duration ?? null,
      llmProcessing: req.phases.llmProcessing?.duration ?? null,
      textToSpeech: req.phases.textToSpeech?.duration ?? null,
    };
  }
}
