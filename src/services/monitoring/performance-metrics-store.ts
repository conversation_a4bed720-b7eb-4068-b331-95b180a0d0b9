import { ConversationMetrics, RequestMetrics, MetricsPhase } from '../../types/monitoring';
import { logMetrics } from '../logging/logging';

/**
 * Stores and manages performance metrics for conversations
 */
export class PerformanceMetricsStore {
  private static instance: PerformanceMetricsStore;
  private metrics: Map<string, ConversationMetrics> = new Map();

  private constructor() {}

  public static getInstance(): PerformanceMetricsStore {
    if (!PerformanceMetricsStore.instance) {
      PerformanceMetricsStore.instance = new PerformanceMetricsStore();
    }
    return PerformanceMetricsStore.instance;
  }

  /**
   * Initialize metrics for a new conversation
   */
  public initializeConversation(conversationId: string, ani: string): void {
    const metrics: ConversationMetrics = {
      conversationId,
      ani,
      startTime: new Date(),
      endTime: new Date(), // Will be updated when conversation ends
      requests: [],
      phaseMetrics: {
        speechToText: {
          avgDuration: 0,
          minDuration: Infinity,
          maxDuration: 0,
          totalDuration: 0,
          count: 0,
        },
        llmProcessing: {
          avgDuration: 0,
          minDuration: Infinity,
          maxDuration: 0,
          totalDuration: 0,
          count: 0,
        },
        textToSpeech: {
          avgDuration: 0,
          minDuration: Infinity,
          maxDuration: 0,
          totalDuration: 0,
          count: 0,
        },
      },
    };

    this.metrics.set(conversationId, metrics);
    logMetrics(`[Metrics] Initialized metrics for conversation ${conversationId}`);
  }

  /**
   * Check if a request with the same user input already exists
   * @returns true if a duplicate request was found
   */
  private isDuplicateRequest(conversationId: string, userInput: string): boolean {
    const metrics = this.getMetrics(conversationId);
    if (!metrics || !userInput) {
      return false;
    }

    // Check the last 3 requests to see if any have the same user input
    const recentRequests = metrics.requests.slice(-3);
    return recentRequests.some(req => req.userInput === userInput);
  }

  /**
   * Initialize metrics for a new request
   */
  public initializeRequest(conversationId: string, requestId: string, userInput = ''): void {
    const metrics = this.getMetrics(conversationId);
    if (!metrics) {
      return;
    }

    // Check for duplicate requests with the same user input
    if (userInput && this.isDuplicateRequest(conversationId, userInput)) {
      // No need to log every skipped duplicate - this happens frequently
      return;
    }

    const requestMetrics: RequestMetrics = {
      requestId,
      timestamp: new Date(),
      userInput: userInput, // Set initial user input if provided
      aiReply: '', // Will be updated when request is finalized
      phases: {
        speechToText: {
          startTime: new Date(0), // Will be updated when phase starts
          endTime: null,
          duration: null,
        },
        llmProcessing: {
          startTime: new Date(0),
          endTime: null,
          duration: null,
        },
        textToSpeech: {
          startTime: new Date(0),
          endTime: null,
          duration: null,
        },
      },
      totalDuration: null,
    };

    metrics.requests.push(requestMetrics);
    logMetrics(
      `[Metrics] Initialized request ${requestId} for conversation ${conversationId}${
        userInput ? ` with input: "${userInput}"` : ''
      }`
    );
  }

  /**
   * Start timing for a specific phase
   */
  public startPhase(conversationId: string, requestId: string, phase: MetricsPhase): void {
    const request = this.getRequest(conversationId, requestId);
    if (!request) {
      return;
    }

    const startTime = new Date();
    request.phases[phase].startTime = startTime;
    logMetrics(`[Metrics] Started ${phase} phase for request ${requestId}`);
  }

  /**
   * End timing for a specific phase
   */
  public endPhase(conversationId: string, requestId: string, phase: MetricsPhase): void {
    const request = this.getRequest(conversationId, requestId);
    if (!request) {
      return;
    }

    const endTime = new Date();
    const phaseMetrics = request.phases[phase];

    // Only update if the phase has a valid start time
    if (phaseMetrics.startTime.getTime() === 0) {
      logMetrics(
        `[Metrics] Cannot end ${phase} phase for request ${requestId}: phase was not started`
      );
      return;
    }

    phaseMetrics.endTime = endTime;
    phaseMetrics.duration = endTime.getTime() - phaseMetrics.startTime.getTime();

    // Update conversation-level phase metrics
    this.updatePhaseMetrics(conversationId, phase, phaseMetrics.duration);

    logMetrics(
      `[Metrics] Ended ${phase} phase for request ${requestId}: ${phaseMetrics.duration}ms`
    );

    // Check if all phases are complete and update total duration
    this.updateTotalDuration(conversationId, requestId);
  }

  /**
   * Update phase metrics at the conversation level
   */
  private updatePhaseMetrics(conversationId: string, phase: MetricsPhase, duration: number): void {
    const metrics = this.getMetrics(conversationId);
    if (!metrics) {
      return;
    }

    const phaseMetrics = metrics.phaseMetrics[phase];
    phaseMetrics.count++;
    phaseMetrics.totalDuration += duration;
    phaseMetrics.avgDuration = phaseMetrics.totalDuration / phaseMetrics.count;
    phaseMetrics.minDuration = Math.min(phaseMetrics.minDuration, duration);
    phaseMetrics.maxDuration = Math.max(phaseMetrics.maxDuration, duration);
  }

  /**
   * Update total duration for a request
   */
  private updateTotalDuration(conversationId: string, requestId: string): void {
    const request = this.getRequest(conversationId, requestId);
    if (!request) {
      return;
    }

    const { speechToText, llmProcessing, textToSpeech } = request.phases;

    // Calculate total duration from completed phases
    let totalDuration = 0;
    let completedPhases = 0;

    if (speechToText.duration !== null) {
      totalDuration += speechToText.duration;
      completedPhases++;
    }

    if (llmProcessing.duration !== null) {
      totalDuration += llmProcessing.duration;
      completedPhases++;
    }

    if (textToSpeech.duration !== null) {
      totalDuration += textToSpeech.duration;
      completedPhases++;
    }

    // Only set total duration if at least one phase is complete
    if (completedPhases > 0) {
      request.totalDuration = totalDuration;
    }
  }

  /**
   * Set user input for a request
   */
  public setUserInput(conversationId: string, requestId: string, userInput: string): void {
    const request = this.getRequest(conversationId, requestId);
    if (!request) {
      return;
    }

    request.userInput = userInput;
    // No need to log every user input set - this happens frequently
  }

  /**
   * Set AI reply for a request
   * If the request is not found by ID, it will try to find a request with matching user input
   */
  public setAiReply(
    conversationId: string,
    requestId: string,
    aiReply: string,
    userInput?: string
  ): void {
    // First try to find the request by ID
    let request = this.getRequest(conversationId, requestId);

    // If not found and userInput is provided, try to find by user input
    if (!request && userInput) {
      request = this.findRequestByUserInput(conversationId, userInput);
    }

    // If still not found, try to find the most recent request with empty AI reply
    if (!request) {
      const metrics = this.getMetrics(conversationId);
      if (metrics) {
        // Find the most recent request with empty AI reply
        request = [...metrics.requests].reverse().find(req => !req.aiReply);
      }
    }

    if (!request) {
      // No need to log when a request can't be found - this is handled gracefully
      return;
    }

    request.aiReply = aiReply;
    // No need to log every AI reply set - this happens frequently
  }

  /**
   * Finalize a request with user input
   */
  public finalizeRequest(
    conversationId: string,
    requestId: string,
    userInput: string,
    aiReply: string
  ): void {
    // First try to find the request by ID
    let request = this.getRequest(conversationId, requestId);

    // If not found and userInput is provided, try to find by user input
    if (!request && userInput) {
      request = this.findRequestByUserInput(conversationId, userInput);
      if (request) {
        requestId = request.requestId; // Update requestId for logging
      }
    }

    // If still not found, try to find the most recent request
    if (!request) {
      const metrics = this.getMetrics(conversationId);
      if (metrics && metrics.requests.length > 0) {
        request = metrics.requests[metrics.requests.length - 1];
        requestId = request.requestId; // Update requestId for logging
      }
    }

    if (!request) {
      // No need to log when a request can't be found - this is handled gracefully
      return;
    }

    // Only update if values are provided
    if (userInput) {
      request.userInput = userInput;
    }

    if (aiReply) {
      request.aiReply = aiReply;
    }

    this.updateTotalDuration(conversationId, requestId);

    // Log the interaction metrics
    this.logInteractionMetrics(conversationId, requestId);
  }

  /**
   * Log metrics for a single interaction
   */
  public logInteractionMetrics(conversationId: string, requestId: string): void {
    const request = this.getRequest(conversationId, requestId);
    if (!request) {
      return;
    }

    const { speechToText, llmProcessing, textToSpeech } = request.phases;
    const asrTime = speechToText.duration !== null ? speechToText.duration : 'N/A';
    const llmTime = llmProcessing.duration !== null ? llmProcessing.duration : 'N/A';
    const ttsTime = textToSpeech.duration !== null ? textToSpeech.duration : 'N/A';
    const totalTime = request.totalDuration !== null ? request.totalDuration : 'N/A';

    logMetrics(
      `[INTERACTION] ConvID: ${conversationId}, ReqID: ${requestId}, ` +
        `User: "${request.userInput}", AI: "${request.aiReply}", ASR: ${asrTime}ms, LLM: ${llmTime}ms, ` +
        `TTS: ${ttsTime}ms, Total: ${totalTime}ms`
    );
  }

  /**
   * Log summary of all interactions in a conversation
   */
  public logConversationSummary(conversationId: string): void {
    const metrics = this.getMetrics(conversationId);
    if (!metrics) {
      return;
    }

    const { requests } = metrics;
    if (requests.length === 0) {
      logMetrics(`[CONVERSATION SUMMARY] ConvID: ${conversationId}, No interactions recorded`);
      return;
    }

    logMetrics(
      `[CONVERSATION SUMMARY] ConvID: ${conversationId}, Interactions: ${requests.length}`
    );

    requests.forEach((request, index) => {
      const { speechToText, llmProcessing, textToSpeech } = request.phases;
      const asrTime = speechToText.duration !== null ? speechToText.duration : 'N/A';
      const llmTime = llmProcessing.duration !== null ? llmProcessing.duration : 'N/A';
      const ttsTime = textToSpeech.duration !== null ? textToSpeech.duration : 'N/A';
      const totalTime = request.totalDuration !== null ? request.totalDuration : 'N/A';

      logMetrics(
        `  ${index + 1}. User: "${request.userInput}", AI: "${
          request.aiReply
        }", ASR: ${asrTime}ms, ` + `LLM: ${llmTime}ms, TTS: ${ttsTime}ms, Total: ${totalTime}ms`
      );
    });
  }

  /**
   * Get a specific request by ID
   */
  private getRequest(conversationId: string, requestId: string): RequestMetrics | undefined {
    const metrics = this.getMetrics(conversationId);
    if (!metrics) {
      return undefined;
    }

    return metrics.requests.find(req => req.requestId === requestId);
  }

  /**
   * Find a request by user input
   * This is useful for finding the right request to associate an AI reply with
   */
  private findRequestByUserInput(
    conversationId: string,
    userInput: string
  ): RequestMetrics | undefined {
    const metrics = this.getMetrics(conversationId);
    if (!metrics || !userInput) {
      return undefined;
    }

    // Look for a request with matching user input, starting from the most recent
    return [...metrics.requests].reverse().find(req => req.userInput === userInput);
  }

  /**
   * Get metrics for a conversation
   */
  private getMetrics(conversationId: string): ConversationMetrics | undefined {
    return this.metrics.get(conversationId);
  }

  /**
   * Clean up duplicate requests in a conversation
   * This removes requests with duplicate user inputs and empty AI replies
   */
  private cleanupDuplicateRequests(conversationId: string): void {
    const metrics = this.getMetrics(conversationId);
    if (!metrics) {
      return;
    }

    // Track user inputs we've seen
    const seenUserInputs = new Set<string>();
    const uniqueRequests: RequestMetrics[] = [];

    // Process requests in reverse order (newest first)
    // This ensures we keep the most recent request for each user input
    for (let i = metrics.requests.length - 1; i >= 0; i--) {
      const request = metrics.requests[i];

      // Skip requests with empty user input
      if (!request.userInput) {
        continue;
      }

      // If we haven't seen this user input before, or this request has an AI reply, keep it
      if (!seenUserInputs.has(request.userInput) || request.aiReply) {
        uniqueRequests.unshift(request); // Add to the beginning to maintain order
        seenUserInputs.add(request.userInput);
      }
    }

    // Update the requests array
    metrics.requests = uniqueRequests;
  }

  /**
   * Finalize conversation metrics
   */
  public finalizeConversation(conversationId: string): void {
    const metrics = this.getMetrics(conversationId);
    if (!metrics) {
      return;
    }

    metrics.endTime = new Date();

    // Clean up duplicate requests before logging summary
    this.cleanupDuplicateRequests(conversationId);

    // Log conversation summary before finalizing
    this.logConversationSummary(conversationId);

    logMetrics(`[Metrics] Finalized metrics for conversation ${conversationId}`);
  }

  /**
   * Get conversation metrics
   */
  public getConversationMetrics(conversationId: string): ConversationMetrics | null {
    return this.metrics.get(conversationId) || null;
  }

  /**
   * Clear metrics for a specific conversation
   */
  public clearConversationMetrics(conversationId: string): void {
    this.metrics.delete(conversationId);
    logMetrics(`[Metrics] Cleared metrics for conversation ${conversationId}`);
  }

  /**
   * Clear all metrics
   */
  public clearAllMetrics(): void {
    this.metrics.clear();
    logMetrics('[Metrics] Cleared all metrics data');
  }

  /**
   * Dispose of the metrics store
   */
  public dispose(): void {
    this.clearAllMetrics();
  }
}
