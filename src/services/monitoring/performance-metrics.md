# Request Phase Metrics Implementation

## Overview

This document outlines the implementation of metrics tracking for three key phases in the request processing pipeline:

1. **Speech to Text**: Time from receiving audio data to completing speech-to-text (ASR processing)
2. **LLM Processing**: Time from having the transcript to getting the LLM response
3. **Text to Speech**: Time from having the LLM text response to completing the audio response delivery

The goal is to provide detailed timing information for each interaction to help identify bottlenecks in the processing pipeline.

## Current Architecture

The existing metrics system tracks:

- Conversation-level metrics (ASR, TTS, Bot processing times)
- System resource utilization (CPU, memory)
- Content length and context size impacts

However, it lacks:

- Request-level tracking for individual user interactions
- End-to-end phase metrics
- Consolidated logging of interaction metrics

## Target Architecture

The enhanced metrics system will:

1. Track metrics at both conversation and request levels
2. Measure the three specific phases for each interaction
3. Log metrics for each interaction in a single line
4. Provide a summary log of all interactions after conversation end

## Implementation Steps

### [x] 1. Update Data Structures

- Create `MetricsPhase` type for the three phases
- Add `PhaseMetrics` interface to track timing for each phase
- Add `RequestMetrics` interface to track metrics for individual requests
- Update `ConversationMetrics` to include request-level metrics

### [x] 2. Enhance PerformanceMetricsStore

- Add methods for initializing and tracking request metrics
- Implement phase tracking functionality
- Add methods for logging interaction metrics and conversation summaries

### [x] 3. Update PerformanceLogger

- Add request ID tracking and generation
- Add methods for starting and ending phase timing
- Add methods for initializing and finalizing requests

### [x] 4. Create Documentation

- Document the metrics system architecture
- Provide usage examples
- Show expected log output

### [x] 5. Integrate with Session Class

- Update Session class to use the performance logger
- Add phase transition tracking at key points in the processing flow

### [ ] 6. Add Automated Tests

- Create unit tests for PerformanceMetricsStore
- Create unit tests for PerformanceLogger
- Test edge cases and error handling
- Create integration tests for the complete metrics flow

### [x] 7. Simplify Metrics Implementation (Phase 1)

- Implement PhaseContext for automatic phase management
- Add trackPhase method to create a PhaseContext
- Add transitionPhase method for phase transitions
- Add fluent API for improved readability

### [x] 7.1 Simplify Metrics Implementation (Phase 2)

- Implement RequestContext for better request handling
- Add createRequest method to create a RequestContext
- Add setCurrentAiReply and getCurrentAiReply methods
- Add tests for RequestContext
- Reduce code duplication and complexity

### [x] 8. Update Session to Use RequestContext

- Update Session class to use RequestContext for better metrics tracking
- Refactor processBotStart to use RequestContext
- Refactor processBotResponse to use RequestContext
- Refactor processBinaryMessage to use RequestContext
- Refactor DTMF handling to use RequestContext
- Improve error handling with try/finally blocks

### [ ] 9. Improve Error Handling

- Add proper error handling for all metrics operations
- Ensure phases are properly ended even in error cases
- Add logging for metrics-related errors
- Make metrics system more resilient to failures

### [ ] 9. Performance Optimization

- Analyze metrics system performance impact
- Optimize memory usage for long-running conversations
- Reduce CPU overhead for high-volume scenarios
- Implement metrics sampling for high-load situations

## Detailed Interface Definitions

### Request Metrics Interface

```typescript
export interface RequestMetrics {
  requestId: string;
  timestamp: Date;
  userInput: string;
  aiReply: string;
  phases: {
    speechToText: PhaseMetrics;
    llmProcessing: PhaseMetrics;
    textToSpeech: PhaseMetrics;
  };
  totalDuration: number | null;
}
```

### PhaseContext Class

```typescript
export class PhaseContext {
  constructor(private logger: PerformanceLogger, private phase: MetricsPhase) {
    this.logger.startPhase(phase);
  }

  /**
   * End the phase and calculate duration
   */
  end(): void {
    this.logger.endPhase(this.phase);
  }
}
```

### RequestContext Class (Planned)

```typescript
export class RequestContext {
  constructor(private logger: PerformanceLogger, public readonly requestId: string) {}

  /**
   * Set the user input for this request
   */
  setUserInput(userInput: string): this {
    this.logger.setCurrentUserInput(userInput);
    return this;
  }

  /**
   * Set the AI reply for this request
   */
  setAiReply(aiReply: string): this {
    this.logger.setCurrentAiReply(aiReply);
    return this;
  }

  /**
   * Create a context for tracking a phase
   */
  trackPhase(phase: MetricsPhase): PhaseContext {
    return this.logger.trackPhase(phase);
  }

  /**
   * Finalize the request with optional AI reply
   */
  finalize(aiReply: string = ''): void {
    this.logger.finalizeRequest('', aiReply || this.logger.getCurrentAiReply());
  }
}
```

### Performance Logger API

```typescript
// Initialize a new request
performanceLogger.initializeRequest(): string;

// Start timing for a specific phase
performanceLogger.startPhase(phase: MetricsPhase): void;

// End timing for a specific phase
performanceLogger.endPhase(phase: MetricsPhase): void;

// Create a context for tracking a phase (automatically starts the phase)
performanceLogger.trackPhase(phase: MetricsPhase): PhaseContext;

// Transition from one phase to another (ends the first phase and starts the second)
performanceLogger.transitionPhase(from: MetricsPhase, to: MetricsPhase): PhaseContext;

// Set the current user input
performanceLogger.setCurrentUserInput(userInput: string): void;

// Set the current AI reply
performanceLogger.setCurrentAiReply(aiReply: string): void;

// Finalize a request with user input and AI reply
performanceLogger.finalizeRequest(userInput: string, aiReply: string): void;

// Finalize conversation metrics
performanceLogger.finalizeConversation(): void;
```

## Usage Examples

### Initialize a Conversation

When a new conversation starts, set the conversation ID in the performance logger:

```typescript
performanceLogger.setConversationId(conversationId);
```

### Track Request Phases (Original API)

For each user request:

1. Initialize a new request when receiving audio:

```typescript
if (!performanceLogger.getCurrentRequestId()) {
  performanceLogger.initializeRequest();
  performanceLogger.startPhase('speechToText');
}
```

2. When transcript is received:

```typescript
performanceLogger.endPhase('speechToText');
performanceLogger.startPhase('llmProcessing');
```

3. When LLM response is received:

```typescript
performanceLogger.endPhase('llmProcessing');
performanceLogger.startPhase('textToSpeech');
```

4. When audio is sent:

```typescript
performanceLogger.endPhase('textToSpeech');
performanceLogger.finalizeRequest(userInput, aiReply);
```

### Track Request Phases (Using PhaseContext)

For each user request:

1. Initialize a new request when receiving audio:

```typescript
if (!performanceLogger.getCurrentRequestId()) {
  performanceLogger.initializeRequest();
  const asr = performanceLogger.trackPhase('speechToText');
  try {
    // ASR processing...
  } finally {
    asr.end(); // Automatically ends the phase, even if an error occurs
  }
}
```

2. When transcript is received:

```typescript
const llm = performanceLogger.trackPhase('llmProcessing');
try {
  // LLM processing...
} finally {
  llm.end();
}
```

3. When LLM response is received:

```typescript
const tts = performanceLogger.trackPhase('textToSpeech');
try {
  // TTS processing...
} finally {
  tts.end();
}
```

4. When audio is sent:

```typescript
performanceLogger.finalizeRequest(userInput, aiReply);
```

### Track Request Phases (Using Phase Transitions)

For each user request:

```typescript
// Start with ASR
const asr = performanceLogger.trackPhase('speechToText');
try {
  // ASR processing...
} finally {
  // Transition to LLM
  const llm = performanceLogger.transitionPhase('speechToText', 'llmProcessing');
  try {
    // LLM processing...
  } finally {
    // Transition to TTS
    const tts = performanceLogger.transitionPhase('llmProcessing', 'textToSpeech');
    try {
      // TTS processing...
    } finally {
      tts.end();
    }
  }
}
```

### Track Request Phases (Using RequestContext - Planned)

For each user request:

```typescript
// Create a request context
const request = performanceLogger.createRequest();

// ASR phase
const asr = request.trackPhase('speechToText');
try {
  // ASR processing...
  request.setUserInput(transcript.text);
} finally {
  asr.end();
}

// LLM phase
const llm = request.trackPhase('llmProcessing');
try {
  // LLM processing...
  request.setAiReply(response.text);
} finally {
  llm.end();
}

// TTS phase
const tts = request.trackPhase('textToSpeech');
try {
  // TTS processing...
} finally {
  tts.end();
}

// Finalize the request
request.finalize();
```

### End a Conversation

When a conversation ends:

```typescript
performanceLogger.finalizeConversation();
```

## Log Output Examples

### Interaction Log

```
[INTERACTION] ConvID: abc123, ReqID: req456, User: "What's the weather today?", ASR: 320ms, LLM: 1250ms, TTS: 450ms, Total: 2020ms
```

### Conversation Summary Log

```
[CONVERSATION SUMMARY] ConvID: abc123, Interactions: 5
  1. User: "Hello", ASR: 280ms, LLM: 950ms, TTS: 380ms, Total: 1610ms
  2. User: "What's the weather today?", ASR: 320ms, LLM: 1250ms, TTS: 450ms, Total: 2020ms
  ...
```

## Configuration

Environment variables for metrics configuration:

```
# Metrics Configuration
ENABLE_PERF_MONITOR=true    # Enable/disable performance monitoring
```

## Integration Points

The metrics system integrates with:

1. **Session Class**: For tracking request lifecycle

   - Audio reception
   - ASR completion
   - LLM processing
   - TTS delivery

2. **WebSocket Handlers**: For tracking events

   - PlaybackStarted
   - PlaybackCompleted

3. **Conversation Management**: For tracking conversation lifecycle
   - Conversation start
   - Conversation end

## Benefits

1. **Detailed Analysis**: Identify specific bottlenecks in the processing pipeline
2. **Per-Request Visibility**: See metrics for each user interaction
3. **Trend Analysis**: Track performance over time and across different request types
4. **Optimization Guidance**: Focus optimization efforts on the slowest phases
5. **Improved Error Handling**: PhaseContext ensures phases are always ended, even in error cases
6. **Reduced Code Duplication**: Common patterns are encapsulated in reusable components
7. **Better Readability**: Fluent API makes the code more declarative and easier to understand
8. **Safer Phase Management**: Automatic phase management reduces the risk of timing errors

---

## Lessons Learned & Best Practices: Accurate Phase Timing

### Background

During implementation and production validation, we observed that phase durations for ASR, LLM, and especially TTS were often 0ms or unrealistically short. This made it impossible to identify true performance bottlenecks.

### Root Cause

- **Phase timing was not managed at the true async boundary.**
  - For TTS, `endPhase` was called immediately after audio was sent to the client, not after playback was actually completed.
  - For LLM and ASR, phase timing was sometimes managed at a higher orchestration level, not within the service layer that performs the actual async work.

### Solution

- **TTS:** The TTS phase is now ended only after the playback completed event is received from the client (e.g., after the `playback_completed` WebSocket message). The phase context is stored when TTS audio is sent and finalized in the playback completion handler, ensuring accurate phase duration and single invocation per request.
- **LLM:** The LLM phase timing wraps the entire async call to the LLM service and response processing, using try/finally to ensure robust finalization.
- **ASR:** The ASR phase timing wraps from the start of audio input processing to the final transcript event, ensuring the phase is measured for the entire utterance.

### Best Practices for Accurate Metrics

- **Always place phase timing at the true async boundary:** Start timing immediately before the async work begins, and end only after the async work is truly complete (including all callbacks, events, or client confirmations).
- **For TTS:** End the phase on playback completion, not on audio send.
- **For LLM:** End the phase after the full LLM response is processed and available to the session.
- **For ASR:** End the phase after the final transcript is emitted.
- **Use try/finally or equivalent patterns** to ensure phases are always ended, even in error cases.
- **Store phase context if needed** to finalize at a later event (e.g., TTS playback completion).
- **Ensure requestId consistency** throughout the async work and metrics calls.
- **Avoid managing phase timing in orchestration/state machine layers**; always place it in the service layer that performs the actual async work.

### Integration Points

- **Playback completion events** (for TTS) must be handled in the audio service layer to finalize the phase.
- **Final transcript events** (for ASR) and **LLM response completion** (for LLM) should be the only points where phases are ended.

### Outcome

With these changes, phase durations now accurately reflect the true time spent in each phase, enabling reliable performance monitoring and bottleneck analysis.

---
## Performance Impact

- Memory overhead: <5%
- CPU overhead: <1%
- Storage: ~100MB/day (varies with usage)
## Limitations, Edge Cases, and Enforcement

### No Strict Enforcement

The system does **not** strictly enforce that all three phases (ASR, LLM, TTS) are measured for every turn. Phase measurement relies on correct usage in the service and state machine layers.

### Implications

- Some turns may have missing or incomplete phase metrics.
- The total duration may not match the sum of the measured phases.
- Metrics may be missing if a phase is not started or ended, or if duplicate/misordered calls occur.

### Known Edge Cases

- **Phase not started or ended:** Can occur due to errors, early exits, or missing events.
- **Duplicate phase ends:** Event handler called multiple times.
- **Total duration mismatch:** If a phase is skipped or measured at the wrong boundary.
- **TTS phase:** Ends when audio is sent to the client (not playback completion), to measure system responsivity.

### Reliance on Correct Usage

Accurate metrics require that phase measurement is implemented at the correct async boundaries in the service layer, not just in orchestration/state machine code. See [Best Practices](#lessons-learned--best-practices-accurate-phase-timing) for recommendations.

### What to Watch For

- Always check logs for missing or zero durations.
- Use `try/finally` to ensure phases are always ended.
- Store phase context if finalization must occur in a later event handler.

### Best Practices Summary

| Phase         | Start Timing                        | End Timing (Best Practice)                |
|---------------|------------------------------------|-------------------------------------------|
| ASR           | Before audio processing starts      | After final transcript is emitted         |
| LLM           | Before LLM call                    | After full LLM response is processed      |
| TTS           | Before TTS synthesis starts        | When audio is sent to the client          |

- Place phase timing at the true async boundary.
- End TTS phase on audio send, not playback completion.
- End LLM phase after full response is processed.
- End ASR phase after final transcript.
- Use `try/finally` to always end phases.
- Store phase context if needed for later finalization.
- Avoid managing phase timing in orchestration/state machine layers.

> For technical flow diagrams, see [docs/application-flow.md](../../../docs/application-flow.md).

---
