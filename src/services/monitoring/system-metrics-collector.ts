import { SystemMetrics } from '../../types/monitoring';
import os from 'os';
import { logInfo, logWarning, logError, logDebug } from './logging';

/**
 * Collects system-level metrics including CPU, memory, and process statistics
 */
export class SystemMetricsCollector {
  private static instance: SystemMetricsCollector;
  private metricsHistory: SystemMetrics[] = [];
  private readonly maxHistoryLength = 8640; // 24 hours of metrics at 10s intervals
  private collectionInterval: NodeJS.Timeout | null = null;

  private constructor() {}

  public static getInstance(): SystemMetricsCollector {
    if (!SystemMetricsCollector.instance) {
      SystemMetricsCollector.instance = new SystemMetricsCollector();
    }
    return SystemMetricsCollector.instance;
  }

  /**
   * Start collecting metrics at specified interval
   * @param intervalMs Collection interval in milliseconds (default: 10000ms)
   */
  public startCollecting(intervalMs = 10000): void {
    if (this.collectionInterval) {
      logWarning('[Metrics] System metrics collection already running');
      return;
    }

    this.collectionInterval = setInterval(() => {
      this.collectMetrics();
    }, intervalMs);

    logInfo(`[Metrics] Started system metrics collection (interval: ${intervalMs}ms)`);
  }

  /**
   * Stop collecting metrics
   */
  public stopCollecting(): void {
    if (this.collectionInterval) {
      clearInterval(this.collectionInterval);
      this.collectionInterval = null;
      logInfo('[Metrics] Stopped system metrics collection');
    }
  }

  /**
   * Get the latest collected metrics
   */
  public getLatestMetrics(): SystemMetrics | null {
    return this.metricsHistory.length > 0
      ? this.metricsHistory[this.metricsHistory.length - 1]
      : null;
  }

  /**
   * Get metrics history for a specific time range
   * @param duration Time range in milliseconds
   */
  public getMetricsHistory(duration: number): SystemMetrics[] {
    const cutoff = Date.now() - duration;
    return this.metricsHistory.filter(metric => metric.timestamp.getTime() > cutoff);
  }

  /**
   * Collect current system metrics
   */
  private collectMetrics(): void {
    try {
      const metrics: SystemMetrics = {
        timestamp: new Date(),
        cpu: this.collectCpuMetrics(),
        memory: this.collectMemoryMetrics(),
        processMetrics: this.collectProcessMetrics(),
      };

      this.metricsHistory.push(metrics);

      // Maintain history length limit
      if (this.metricsHistory.length > this.maxHistoryLength) {
        this.metricsHistory = this.metricsHistory.slice(-this.maxHistoryLength);
      }
    } catch (error) {
      logError(
        `[Metrics] Error collecting system metrics: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }

  private collectCpuMetrics() {
    const cpus = os.cpus();
    const loadAvg = os.loadavg();

    // Calculate CPU usage percentage
    const totalIdle = cpus.reduce((acc, cpu) => acc + cpu.times.idle, 0);
    const totalTick = cpus.reduce(
      (acc, cpu) => acc + Object.values(cpu.times).reduce((sum, time) => sum + time, 0),
      0
    );

    const usage = ((totalTick - totalIdle) / totalTick) * 100;

    return {
      usage,
      loadAverage: loadAvg,
    };
  }

  private collectMemoryMetrics() {
    const totalMem = os.totalmem();
    const freeMem = os.freemem();
    const usedMem = totalMem - freeMem;

    // Get V8 memory usage
    const heapStats = process.memoryUsage();

    return {
      total: totalMem,
      used: usedMem,
      free: freeMem,
      heapTotal: heapStats.heapTotal,
      heapUsed: heapStats.heapUsed,
      rss: heapStats.rss,
    };
  }

  private collectProcessMetrics() {
    return {
      uptime: process.uptime(),
      // Using process.getActiveResourcesInfo() for Node.js >=16
      activeHandles: 0, // This will be updated in a future PR with proper resource tracking
      activeRequests: 0, // This will be updated in a future PR with proper resource tracking
    };
  }

  /**
   * Clear all collected metrics
   */
  public clearMetrics(): void {
    this.metricsHistory = [];
    logDebug('[Metrics] Cleared system metrics history');
  }
}
