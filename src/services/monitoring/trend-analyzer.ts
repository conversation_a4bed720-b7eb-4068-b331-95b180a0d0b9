import { TrendMetrics, ConversationMetrics } from '../../types/monitoring';

interface TimeRangeConfig {
  id: string;
  duration: number; // in milliseconds
}

/**
 * Analyzes performance trends based on content length and context size
 */
export class TrendAnalyzer {
  private static instance: TrendAnalyzer;
  private trendsData: Map<string, TrendMetrics> = new Map();

  // Time ranges for trend analysis
  private readonly timeRanges: TimeRangeConfig[] = [
    { id: '1h', duration: 3600000 }, // 1 hour
    { id: '24h', duration: 86400000 }, // 24 hours
    { id: '7d', duration: 604800000 }, // 7 days
  ];

  private constructor() {}

  public static getInstance(): TrendAnalyzer {
    if (!TrendAnalyzer.instance) {
      TrendAnalyzer.instance = new TrendAnalyzer();
    }
    return TrendAnalyzer.instance;
  }

  /**
   * Update trends with new conversation data
   */
  public updateTrends(_metrics: ConversationMetrics): void {
    try {
      this.timeRanges.forEach(range => {
        const trendMetrics = this.getTrendMetrics(range.id);
        // TODO: Refactor trend updating logic to use metrics.requests and phase timing (see performance-metrics.md)
        this.trendsData.set(range.id, trendMetrics);
      });
    } catch (error) {
      console.error('[Metrics] Error updating trends:', error);
    }
  }

  /**
   * Get trend metrics for a specific time range
   */
  private getTrendMetrics(timeRange: string): TrendMetrics {
    return this.trendsData.get(timeRange) || this.createEmptyTrendMetrics(timeRange);
  }

  /**
   * Create empty trend metrics structure
   */
  private createEmptyTrendMetrics(timeRange: string): TrendMetrics {
    return {
      timeRange,
      contentCategory: {
        xs: 0,
        s: 0,
        m: 0,
        l: 0,
      },
      averageProcessingTimes: {
        xs: 0,
        s: 0,
        m: 0,
        l: 0,
      },
      contextSizeImpact: {
        smallContext: 0,
        mediumContext: 0,
        largeContext: 0,
      },
    };
  }

  /**
   * Update trend metrics with conversation data
   */
  // TODO: Trend analysis must be refactored to use ConversationMetrics.requests and phase timing data.
  // See performance-metrics.md for the current data model and guidance.
  // The previous implementation referenced properties (botResponses, contentLength, processingTime, currentContextSize)
  // that do not exist in the current model.

  /**
   * Calculate average processing time based on context size
   */
  // (Removed calculateContextMetrics; see above TODO)

  // Method removed as it was unused

  /**
   * Get trends for a specific time range
   */
  public getTrends(timeRange: string): TrendMetrics | null {
    return this.trendsData.get(timeRange) || null;
  }

  /**
   * Get all available trends
   */
  public getAllTrends(): Map<string, TrendMetrics> {
    return new Map(this.trendsData);
  }

  /**
   * Clear all trend data
   */
  public clearTrends(): void {
    this.trendsData.clear();
    console.log('[Metrics] Cleared all trend data');
  }
}
