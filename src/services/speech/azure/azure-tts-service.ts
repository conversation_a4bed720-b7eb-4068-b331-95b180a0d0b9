import { SpeechConfig, SpeechSynthesizer } from 'microsoft-cognitiveservices-speech-sdk';
import { BaseTTSService } from '../base/base-tts-service';

export class AzureTTSService extends BaseTTSService {
  private speechConfig: SpeechConfig | null = null;
  private synthesizer: SpeechSynthesizer | null = null;

  protected async initializeService(): Promise<void> {
    //try {
    console.log('[TTS] Initializing Azure TTS service...');
    const subscriptionKey = process.env.AZURE_SPEECH_KEY;
    const region = process.env.AZURE_SPEECH_REGION;

    if (!subscriptionKey || !region) {
      throw new Error('Azure TTS configuration requires AZURE_SPEECH_KEY and AZURE_SPEECH_REGION');
    }

    this.speechConfig = SpeechConfig.fromSubscription(subscriptionKey, region);
    this.speechConfig.speechSynthesisVoiceName = 'cs-CZ-AntoninNeural';
    this.speechConfig.speechSynthesisOutputFormat = 6; // 8kHz PCM µ-law

    // Test synthesizer creation
    this.synthesizer = new SpeechSynthesizer(this.speechConfig);

    // Test synthesis with a small sample
    //   await new Promise<void>((resolve, reject) => {
    //     this.synthesizer!.speakTextAsync(
    //       "Test",
    //       (result) => {
    //         if (result && result.audioData) {
    //           resolve();
    //         } else {
    //           reject(new Error("Failed to synthesize test audio"));
    //         }
    //       },
    //       (error) => {
    //         reject(error);
    //       }
    //     );
    //   });

    //   console.log("[TTS] Azure TTS service initialized");
    // } catch (error) {
    //   console.error(
    //     "[Error] Failed to initialize Azure TTS service:",
    //     error instanceof Error ? error.message : "Unknown error"
    //   );
    //   throw error;
    // }
  }

  protected async synthesizeAudio(text: string): Promise<Uint8Array> {
    if (!this.synthesizer || !this.speechConfig) {
      throw new Error('TTS service not properly initialized');
    }

    try {
      // Check cache first
      const cachedAudio = await this.getCachedAudio(text);
      if (cachedAudio) {
        return cachedAudio;
      }

      // Synthesize speech
      const audioData = await new Promise<Uint8Array>((resolve, reject) => {
        this.synthesizer!.speakSsmlAsync(
          text,
          result => {
            if (result && result.audioData) {
              resolve(new Uint8Array(result.audioData));
            } else {
              reject(new Error('No audio data received from Azure TTS'));
            }
          },
          error => {
            reject(error);
          }
        );
      });

      // Cache the audio for future use
      await this.cacheAudio(text, audioData);

      return audioData;
    } catch (error) {
      console.error(
        '[Error] Error in speech synthesis:',
        error instanceof Error ? error.message : 'Unknown error'
      );
      throw error;
    }
  }

  dispose(): void {
    try {
      if (this.synthesizer) {
        this.synthesizer.close();
        this.synthesizer = null;
      }
      if (this.speechConfig) {
        this.speechConfig = null;
      }
    } catch (error) {
      console.error(
        '[Error] Failed to clean up Azure TTS service:',
        error instanceof Error ? error.message : 'Unknown error'
      );
    }
  }
}
