import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';

/**
 * Abstract base class for Text-to-Speech services
 * Provides common functionality like audio caching and file management
 */
export abstract class BaseTTSService {
  protected readonly audioDir: string;
  private initialized = false;
  private initializationPromise: Promise<void> | null = null;

  constructor() {
    this.audioDir = path.join(process.cwd(), 'debug-audio');
    this.ensureAudioDirectory();
    // Initialize immediately and track initialization status
    this.initializationPromise = this.initializeService()
      .then(() => {
        this.initialized = true;
      })
      .catch(error => {
        console.error('[TTS] Initialization failed:', error);
        throw error;
      });
  }

  /**
   * Initialize the specific TTS service implementation
   * Each provider will implement their own initialization logic
   */
  protected abstract initializeService(): Promise<void>;

  /**
   * Ensure initialization is complete before proceeding
   */
  protected async ensureInitialized(): Promise<void> {
    if (!this.initializationPromise) {
      throw new Error('Service not properly constructed');
    }
    await this.initializationPromise;
    if (!this.initialized) {
      throw new Error('TTS service not properly initialized');
    }
  }

  /**
   * Convert text to speech and return audio bytes
   * @param text Text or SSML to convert to speech
   * @returns Promise<Uint8Array> Audio data as Uint8Array in 8kHz PCMU (µ-law) format
   */
  async getAudioBytes(text: string): Promise<Uint8Array> {
    await this.ensureInitialized();
    return this.synthesizeAudio(text);
  }

  /**
   * Implementation specific audio synthesis
   */
  protected abstract synthesizeAudio(text: string): Promise<Uint8Array>;

  /**
   * Clean up resources used by the TTS service
   */
  abstract dispose(): void;

  /**
   * Ensure the audio cache directory exists
   */
  protected ensureAudioDirectory(): void {
    if (!fs.existsSync(this.audioDir)) {
      fs.mkdirSync(this.audioDir, { recursive: true });
    }
  }

  /**
   * Generate a unique filename for caching audio based on input text
   */
  protected generateAudioFilename(text: string): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -4); // Remove milliseconds 'Z'
    const hash = crypto.createHash('md5').update(text).digest('hex').slice(0, 8); // Use first 8 chars as ID
    return path.join(this.audioDir, `TTS_${hash}_${timestamp}.wav`);
  }

  /**
   * Check if audio is cached and return it if available
   * @param text The text used to generate the audio
   * @returns Promise<Uint8Array | null> The cached audio data or null if not found
   */
  protected async getCachedAudio(text: string): Promise<Uint8Array | null> {
    const audioFilePath = this.generateAudioFilename(text);
    if (fs.existsSync(audioFilePath)) {
      console.log('[TTS] Using cached audio file:', audioFilePath);
      return new Uint8Array(await fs.promises.readFile(audioFilePath));
    }
    return null;
  }

  /**
   * Save audio data to cache
   * @param text The text used to generate the audio
   * @param audioData The audio data to cache
   */
  protected async cacheAudio(text: string, audioData: Uint8Array): Promise<void> {
    const audioFilePath = this.generateAudioFilename(text);
    try {
      await fs.promises.writeFile(audioFilePath, audioData);
      console.log('[TTS] Saved audio file:', audioFilePath);
    } catch (writeError) {
      console.log('[Error] Failed to save audio file:', writeError);
    }
  }
}
