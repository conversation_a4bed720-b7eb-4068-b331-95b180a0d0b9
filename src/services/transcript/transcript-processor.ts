// TranscriptProcessor: <PERSON>les duplicate transcript detection and barge-in flag logic.
import { logInfo, logDebug } from '../monitoring/logging';
import { loggingConfig } from '../logging/logging-config';

export class TranscriptProcessor {
  private lastProcessedTranscript = '';
  private lastProcessedTimestamp = 0;
  private justHadBargeIn = false;

  setBargeInFlag(value: boolean): void {
    this.justHadBargeIn = value;
  }

  /**
   * Checks if the given transcript is a duplicate.
   * @param text The current transcript text.
   * @param asrStreamingMode (optional) The ASR streaming mode, if available.
   */
  isDuplicate(text: string, asrStreamingMode?: string): boolean {
    const currentTime = Date.now();
    const timeSinceLastProcessed = currentTime - this.lastProcessedTimestamp;
    const isDuplicate = text === this.lastProcessedTranscript && timeSinceLastProcessed < 5000;

    // Only log detailed duplicate checks if enabled
    if (loggingConfig.logDuplicateChecks) {
      logDebug(
        `[TranscriptProcessor] Duplicate check:
  Current transcript: "${text}"
  Last processed transcript: "${this.lastProcessedTranscript}"
  Time since last processed: ${timeSinceLastProcessed}ms
  justHadBargeIn: ${this.justHadBargeIn}
  ASR streaming mode: ${asrStreamingMode ?? 'unknown'}
  Result: ${isDuplicate ? 'DUPLICATE' : 'UNIQUE'}`
      );
    }

    if (this.justHadBargeIn) {
      this.justHadBargeIn = false;
      logInfo(
        `[BOT] Processing barge-in text: "${text}" (ASR mode: ${asrStreamingMode ?? 'unknown'})`
      );
      return false;
    }

    if (isDuplicate) {
      // Log duplicates at debug level to reduce noise
      logDebug(
        `[BOT] Ignoring duplicate transcript: "${text}" (processed ${timeSinceLastProcessed}ms ago, ASR mode: ${
          asrStreamingMode ?? 'unknown'
        })`
      );
      return true;
    }

    // Update tracking for non-barge-in transcripts
    this.updateLastProcessed(text);
    return false;
  }

  updateLastProcessed(transcript: string): void {
    this.lastProcessedTranscript = transcript;
    this.lastProcessedTimestamp = Date.now();
  }
}
