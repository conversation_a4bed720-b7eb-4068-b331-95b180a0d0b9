/**
 * Extension: Ensure the session can be prepared for new user input (for state machine entry actions).
 */
/**
 * Interface defining the public API of the Session class.
 * This interface is used by state machine actions to interact with the session.
 */

import type { PlaybackState } from '../services/barge-in/barge-in-manager';
import type { SessionState } from './session-state-manager';
import type { EventDrivenStateManager } from './state-machine/event-driven-state-manager';
import type { EventEmitter } from '../services/events/event-emitter';
import type { ServerMessage } from '../protocol/message';
import type { BotTurnDisposition } from '../protocol/voice-bots';
import type { SessionMetricsAdapter } from '../services/monitoring/session-metrics-adapter';
import type { BotResponse } from '../services/bot-service/bot-response';
import type { RequestContext } from '../services/monitoring';
import type { BaseTTSService } from '../services/speech/base/base-tts-service';

export interface ISession {
  /**
   * Prepare the session for new user input (e.g., after playback).
   * Called by the state machine when entering the IDLE state.
   */
  prepareForUserInput(): void;
  /**
   * Unique identifier for this session
   */
  id: string;

  /**
   * Get the current session state
   */
  getSessionState(): SessionState;

  /**
   * Get the start time of this session
   */
  getStartTime(): Date;

  /**
   * Set the playback state
   * @param state The new playback state
   */
  setPlaybackState(state: PlaybackState): void;

  /**
   * Update playback state without triggering a session state transition
   * @param state The new playback state
   */
  updatePlaybackStateWithoutTransition(state: PlaybackState): void;

  /**
   * Enable barge-in detection
   */
  enableBargeIn(): void;

  /**
   * Disable barge-in detection
   */
  disableBargeIn(): void;

  /**
   * Check if barge-in detection is enabled
   * @returns True if barge-in detection is enabled, false otherwise
   */
  isBargeInEnabled(): boolean;

  /**
   * Get the bot adapter for this session (for use by state actions)
   */
  getBotAdapter(): import('../services/bot-service/bot-service-adapter').BotServiceAdapter;

  /**
   * Get the current request context (for use by state actions)
   */
  getCurrentRequest(): import('../services/monitoring').RequestContext | undefined;

  /**
   * Set the current request context (for use by state actions)
   * @param context The new request context
   */
  setCurrentRequest(context: import('../services/monitoring').RequestContext): void;

  /**
   * Get the startMetricsRequest function (for use by state actions)
   */
  getStartMetricsRequest(): ((input: string) => void) | undefined;

  /**
   * Initialize the ASR service with the conversation ID
   * @param conversationId The conversation ID to use for ASR
   */
  initializeASRService(conversationId: string): Promise<void>;

  /**
   * Process the start of a bot interaction
   * This is called when transitioning to the PROCESSING_BOT state
   *
   * @param isInitialGreeting Whether this is the initial greeting (no user input)
   */
  processBotStart(isInitialGreeting?: boolean): Promise<void>;

  /**
   * Get the conversation ID for this session
   * @returns The conversation ID or undefined if not set
   */
  getConversationId(): string | undefined;

  /**
   * Set the conversation ID for this session
   * @param conversationId The conversation ID
   */
  setConversationId(conversationId: string): void;

  /**
   * Set the ANI (Automatic Number Identification) for this session
   * @param ani The ANI value
   */
  setClientAni(ani: string | undefined): void;

  /**
   * Close the session and clean up resources
   */
  close(): void;

  /**
   * Send a message to the client
   * @param message The message to send
   */
  send(message: ServerMessage): void;

  /**
   * Send a bot turn response event to the client
   * @param text The text response from the bot
   * @param disposition The disposition of the bot turn
   */
  sendBotTurnResponse(text: string, disposition: BotTurnDisposition): void;

  /**
   * Process binary audio data from the client
   * @param data The binary audio data to process
   */
  processBinaryMessage(data: Uint8Array): Promise<void>;

  /**
   * Get the session state manager
   * @returns The session state manager instance
   */
  getSessionStateManager(): EventDrivenStateManager;

  /**
   * Get the event emitter
   * @returns The event emitter instance
   */
  getEventEmitter(): EventEmitter;

  /**
   * Get the metrics adapter
   * @returns The session metrics adapter instance
   */
  getMetricsAdapter(): SessionMetricsAdapter;
  /**
   * Get the latest bot response for this session, if any.
   * @returns The latest BotResponse or undefined.
   */
  getLatestBotResponse(): BotResponse | undefined;

  /**
   * Set the latest bot response for this session.
   * @param response The bot response to set.
   */
  setLatestBotResponse(response: BotResponse | undefined): void;

  /**
   * Send audio bytes to the client.
   * @param audioBytes The audio data to send.
   * @param context Optional request context for metrics.
   */
  sendAudio(audioBytes: Uint8Array, context?: RequestContext): Promise<void>;
  /**
   * Get the TTS service for this session, if available.
   */
  getTTSService(): BaseTTSService | undefined;

  /**
   * Dispose of audio resources for this session.
   */
  disposeAudio(): Promise<void>;

  /**
   * Dispose of bot resources for this session.
   */
  disposeBot(): Promise<void>;

  /**
   * Close the WebSocket connection for this session.
   */
  closeWebSocket(): void;

  /**
   * Finalize metrics for this session.
   */
  finalizeMetrics(): void;

  /**
   * Get the current processing abort controller, if any.
   * This is used to cancel ongoing asynchronous processing (e.g., during barge-in).
   * @returns The current AbortController or undefined if none exists.
   */
  getCurrentProcessingAbortController?(): AbortController | null;

  /**
   * Set the current processing abort controller.
   * This is used to store a reference to an AbortController for cancelling ongoing processing.
   * @param controller The AbortController to set, or null to clear it.
   */
  setCurrentProcessingAbortController?(controller: AbortController | null): void;

  /**
   * Refresh response handling.
   * This is called when a bot response is received asynchronously after we've already
   * transitioned to RESPONDING state. It allows the session to retry playback or
   * other response handling that might have failed due to the response not being available.
   */
  refreshResponse?(): void;
}
