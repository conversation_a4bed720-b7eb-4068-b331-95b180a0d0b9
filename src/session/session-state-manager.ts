/**
 * SessionStateManager: Comprehensive state management for Session lifecycle
 *
 * This class implements a robust state machine pattern for managing session states.
 * It enforces valid state transitions, provides state checking methods, and
 * supports event listeners for state changes.
 *
 * Key features:
 * - Explicit state enumeration
 * - Validated state transitions
 * - Event-based notification system
 * - Helper methods for common state checks
 * - Detailed logging of state changes
 */

// This file only contains type definitions and enums, no imports needed

export enum SessionState {
  INITIALIZING = 'INITIALIZING', // Session is being initialized
  IDLE = 'IDLE', // Session is waiting for user input
  PROCESSING_INPUT = 'PROCESSING_INPUT', // Session is processing user input (ASR/DTMF)
  PROCESSING_BOT = 'PROCESSING_BOT', // Session is waiting for bot response
  RESPONDING = 'RESPONDING', // Session is preparing response (TTS)
  PLAYING = 'PLAYING', // Session is playing audio to user
  DISCONNECTING = 'DISCONNECTING', // Session is in the process of disconnecting
  CLOSED = 'CLOSED', // Session is closed and resources released
}

/**
 * Represents a state transition with metadata
 */
export interface StateTransition {
  from: SessionState;
  to: SessionState;
  timestamp: number;
  reason?: string;
}

/**
 * Listener for state change events
 */
export type StateChangeListener = (
  oldState: SessionState,
  newState: SessionState,
  metadata?: Record<string, unknown>
) => void;
