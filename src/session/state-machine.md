# State Machine: Event-Driven Architecture

## 1. Event-Driven State Machine Design

- **Event-Driven Transitions:** State transitions are triggered by well-defined events, not direct method calls.
- **Decoupled Components:** Components communicate through events, reducing tight coupling.
- **Single Authority:** The `EnhancedSessionStateManager` is the only component that can change state, but it responds to events from various sources.
- **Predictable Flow:** Events trigger transitions in a consistent, predictable way, making the system easier to reason about.

## 2. Core Event Types

- **User Input Events:** Triggered when speech or DTMF input is received.
- **Bot Response Events:** Triggered when a bot response is received.
- **Playback Events:** Triggered when audio playback starts, completes, or is interrupted.
- **Barge-In Events:** Triggered when a user interrupts the system during playback.
- **Session Events:** Triggered for session initialization, closure, and other lifecycle events.

## 3. Initial Greeting vs. Normal Bot Response Flow

- **Initial Greeting:**

  - Session emits an `INITIAL_GREETING_REQUESTED` event.
  - Bot adapter processes the event and emits a `BOT_RESPONSE_RECEIVED` event.
  - State manager transitions to `PROCESSING_BOT` in response to the event.
  - When processing completes, a `BOT_PROCESSING_COMPLETED` event triggers transition to `RESPONDING`.

- **Normal Bot Response:**
  - User input triggers a `USER_INPUT_RECEIVED` event.
  - State manager transitions to `PROCESSING_INPUT` in response.
  - When input processing completes, a `USER_INPUT_PROCESSED` event triggers transition to `PROCESSING_BOT`.
  - Bot response triggers a `BOT_RESPONSE_RECEIVED` event.
  - When bot processing completes, a `BOT_PROCESSING_COMPLETED` event triggers transition to `RESPONDING`.

## 4. Benefits of Event-Driven Architecture

- **Reduced Coupling:** Components interact through events, not direct method calls.
- **Improved Testability:** Events can be mocked for testing, making components easier to test in isolation.
- **Better Error Handling:** Events can include error information, making error propagation more consistent.
- **Enhanced Extensibility:** New components can subscribe to existing events without modifying existing code.
- **Clearer Intent:** Events clearly communicate the intent of state transitions.
## 5. Standard Metrics Flow

This section describes the step-by-step metrics phase tracking in the normal (non-barge-in) flow. Each phase is started and ended explicitly for accurate request-level metrics.

### Metrics Phases

- **speechToText**: User speech is being transcribed (ASR).
- **llmProcessing**: Bot/LLM is processing the input and generating a response.
- **textToSpeech**: TTS synthesis and/or audio playback of the bot response.

### Step-by-Step Flow

1. **User Input (Speech) Begins**
   - `metricsAdapter.startPhase('speechToText')` is called at the start of user input processing (PROCESSING_INPUT state).
2. **Final Transcript Received**
   - `metricsAdapter.endPhase('speechToText')` is called after the final transcript is processed.
3. **Bot Processing Starts**
   - `metricsAdapter.startPhase('llmProcessing')` is called when bot/LLM processing begins (typically at transition to PROCESSING_BOT).
4. **Bot Response Received**
   - `metricsAdapter.endPhase('llmProcessing')` is called as soon as the bot response is received (BOT_RESPONSE_RECEIVED event).
5. **TTS Synthesis/Playback Starts**
   - `metricsAdapter.startPhase('textToSpeech')` is called at the start of TTS synthesis or audio playback (RESPONDING state).
6. **Playback Completes**
   - `metricsAdapter.endPhase('textToSpeech')` is called when playback completes (IDLE state).

All metrics calls are associated with the current requestId and phase type for accurate tracking.

## 5. Deferred Session Closure & Async Events

- **Deferred Closure:** If a `SESSION_CLOSE_REQUESTED` event is received during critical processing, the event is queued.
- **Event Priority:** Events have priorities, ensuring critical events (like barge-in) are processed before lower-priority events.
- **Async Event Processing:** Events are processed asynchronously, allowing for non-blocking operation.
- **Event Completion:** Events can include callbacks to be executed when processing completes.

## 6. State Machine Implementation

### States and Their Purpose

- **INITIALIZING:** Session is being initialized, components are being set up
- **IDLE:** Session is waiting for user input
- **PROCESSING_INPUT:** Session is processing user input (ASR/DTMF)
- **PROCESSING_BOT:** Session is waiting for bot response
- **RESPONDING:** Session is preparing response (TTS)
- **PLAYING:** Session is playing audio to user
- **DISCONNECTING:** Session is in the process of disconnecting
- **CLOSED:** Session is closed and resources released

### EventEmitter

The core event system that enables communication between components:

- Provides a simple, type-safe event subscription and emission mechanism
- Supports event priorities to ensure critical events are processed first
- Handles error catching and logging for event listeners
- Allows components to subscribe to and emit events without direct coupling

### EnhancedSessionStateManager

The state manager that responds to events and manages state transitions:

- Maintains the current state
- Subscribes to relevant events that should trigger state transitions
- Validates state transitions against the transition graph
- Executes entry and exit actions for states
- Tracks state history and durations
- Emits state change events that other components can subscribe to

### Event Handlers

Event handlers process specific events and determine what actions to take:

- **UserInputEventHandler:** Processes user input events and triggers state transitions
- **BotResponseEventHandler:** Processes bot response events and triggers state transitions
- **PlaybackEventHandler:** Processes playback events and triggers state transitions
- **BargeInEventHandler:** Processes barge-in events and triggers state transitions
- **SessionEventHandler:** Processes session lifecycle events and triggers state transitions

**Implementation Note:**
The bot response (text and audio) is set on the session when a `BOT_RESPONSE_RECEIVED` event is emitted.
This ensures the response is always available for the RESPONDING and PLAYING states, and prevents race conditions or missed playback.

## 7. Event-Driven Transition Pattern

Components emit events to signal that something has happened, and the state manager subscribes to these events to determine when state transitions should occur.

**Key points:**

- Components emit events but do not directly trigger state transitions.
- The state manager subscribes to events and decides whether to transition based on the current state and event data.
- Events include all necessary data for the state manager to make transition decisions.
- This pattern ensures that state transitions are centralized and predictable.

This pattern replaces the previous approach of direct method calls and transition requests.

## 8. Valid State Transitions

The state machine still enforces valid transitions between states, but now these transitions are triggered by events rather than direct method calls.

Key event-triggered transitions include:

- **INITIALIZING → IDLE:** `SESSION_INITIALIZED` event
- **IDLE → PROCESSING_INPUT:** `USER_INPUT_RECEIVED` event
- **IDLE → PROCESSING_BOT:** `INITIAL_GREETING_REQUESTED` event
- **PROCESSING_INPUT → PROCESSING_BOT:** `USER_INPUT_PROCESSED` event
- **PROCESSING_BOT → RESPONDING:** `BOT_RESPONSE_RECEIVED` event
- **RESPONDING → PLAYING:** `RESPONSE_PREPARATION_COMPLETED` event
- **PLAYING → IDLE:** `PLAYBACK_COMPLETED` event
- **PLAYING → PROCESSING_INPUT:** `BARGE_IN_DETECTED` event
- **Any state → DISCONNECTING:** `SESSION_CLOSE_REQUESTED` event
- **DISCONNECTING → CLOSED:** `DISCONNECTION_COMPLETED` event

Each event includes metadata that the state manager uses to validate and process the transition.

## 9. Event-Driven Flow

The event-driven state transition process follows these steps:

1. A component emits an event with relevant data
2. The state manager's event handler receives the event
3. The handler checks if the event should trigger a state transition based on the current state
4. If a transition is needed, the state manager validates the transition against the transition graph
5. The state manager executes exit actions for the current state
6. The state is updated to the new state
7. The state manager emits a state change event
8. The state manager executes entry actions for the new state

Example of an event emission:

```typescript
// Emit an event when user input is received
eventEmitter.emit(SessionEventType.USER_INPUT_RECEIVED, {
  input: userTranscript,
  inputType: 'speech',
  timestamp: Date.now(),
});
```

## 10. Event-Driven State Machine Flow Diagram

```mermaid
flowchart LR
    %% Main States
    INIT[INITIALIZING]
    IDLE[IDLE]
    PROC_IN[PROCESSING_INPUT]
    PROC_BOT[PROCESSING_BOT]
    RESP[RESPONDING]
    PLAY[PLAYING]
    DISC[DISCONNECTING]
    CLOSED[CLOSED]

    %% Event-driven transitions
    INIT -->|"SESSION_INITIALIZED event"| IDLE
    INIT -->|"SESSION_INITIALIZATION_FAILED event"| DISC

    IDLE -->|"USER_INPUT_RECEIVED event"| PROC_IN
    IDLE -->|"INITIAL_GREETING_REQUESTED event"| PROC_BOT
    IDLE -->|"DIRECT_PLAYBACK_REQUESTED event"| PLAY
    IDLE -->|"SESSION_CLOSE_REQUESTED event"| DISC
    IDLE -->|"SESSION_CLEAN_SHUTDOWN event"| CLOSED

    PROC_IN -->|"USER_INPUT_PROCESSED event"| PROC_BOT
    PROC_IN -->|"NO_VALID_INPUT_DETECTED event"| IDLE
    PROC_IN -->|"SESSION_CLOSE_REQUESTED event"| DISC

    PROC_BOT -->|"BOT_RESPONSE_RECEIVED event"| RESP
    PROC_BOT -->|"NO_BOT_RESPONSE event"| IDLE
    PROC_BOT -->|"SESSION_CLOSE_REQUESTED event"| DISC

    RESP -->|"RESPONSE_PREPARATION_COMPLETED event"| PLAY
    RESP -->|"NO_AUDIO_TO_PLAY event"| IDLE
    RESP -->|"SESSION_CLOSE_REQUESTED event"| DISC

    PLAY -->|"PLAYBACK_COMPLETED event"| IDLE
    PLAY -->|"BARGE_IN_DETECTED event"| PROC_IN
    PLAY -->|"SESSION_CLOSE_REQUESTED event"| DISC

    DISC -->|"DISCONNECTION_COMPLETED event"| CLOSED

    %% Event Emitters
    classDef emitterClass fill:#e1f5fe,stroke:#0288d1,stroke-width:1px,color:#01579b

    SESSION[Session]:::emitterClass
    ASR[ASR Service]:::emitterClass
    BOT[Bot Service]:::emitterClass
    BARGE[Barge-In Manager]:::emitterClass
    AUDIO[Audio Manager]:::emitterClass

    %% Event flows
    SESSION -->|"emits"| INIT
    SESSION -->|"emits"| IDLE
    ASR -->|"emits"| PROC_IN
    BOT -->|"emits"| PROC_BOT
    AUDIO -->|"emits"| RESP
    AUDIO -->|"emits"| PLAY
    BARGE -->|"emits"| PLAY
    SESSION -->|"emits"| DISC
    SESSION -->|"emits"| CLOSED

    %% State descriptions
    classDef stateDesc fill:#f9f9f9,stroke:#ccc,stroke-width:1px,color:#333

    INIT_DESC["Session is being initialized\nComponents being set up"]:::stateDesc
    IDLE_DESC["Session is waiting for user input"]:::stateDesc
    PROC_IN_DESC["Processing user input (ASR/DTMF)"]:::stateDesc
    PROC_BOT_DESC["Session is waiting for bot response"]:::stateDesc
    RESP_DESC["Preparing response (TTS)"]:::stateDesc
    PLAY_DESC["Playing audio to user"]:::stateDesc
    DISC_DESC["Session is in the process of disconnecting"]:::stateDesc
    CLOSED_DESC["Session is closed and resources released"]:::stateDesc

    %% Connect descriptions to states
    INIT --- INIT_DESC
    IDLE --- IDLE_DESC
    PROC_IN --- PROC_IN_DESC
    PROC_BOT --- PROC_BOT_DESC
    RESP --- RESP_DESC
    PLAY --- PLAY_DESC
    DISC --- DISC_DESC
    CLOSED --- CLOSED_DESC
```

---

For further context, see [`src/common/session.md`](../common/session.md) and the main project README.

## 11. Event-Driven Barge-In and Pause Detection

The event-driven architecture provides a natural way to handle high-priority events such as barge-in and pause detection.

**Implementation Note:**
Pause detection is handled by `PauseDetectionService`, which monitors ASR transcript events and emits a `PAUSE_DETECTED` event when a natural pause is detected. The state manager subscribes to this event and processes it according to the current state.

### Event Priority System

- Events have priorities (HIGH, MEDIUM, LOW) to ensure critical events are processed first.
- High-priority events (like barge-in) are processed immediately, even if other events are pending.
- The event emitter maintains a priority queue to ensure events are processed in the correct order.

### Barge-In Event Flow

1. The `BargeInManager` detects a barge-in and emits a `BARGE_IN_DETECTED` event with HIGH priority.
2. The state manager's event handler receives the event and processes it immediately.
3. Based on the current state, the handler triggers the appropriate state transition:
   - If in PLAYING state, transition to PROCESSING_INPUT
   - If in RESPONDING state, transition to PROCESSING_INPUT
4. The state transition cancels any ongoing operations and processes the user input.

### Pause Detection Event Flow

1. The `PauseDetectionService` detects a pause and emits a `PAUSE_DETECTED` event with MEDIUM priority.
2. The state manager's event handler receives the event and processes it based on the current state.
3. If in PROCESSING_INPUT state, the handler finalizes the current transcript and emits a `USER_INPUT_PROCESSED` event.
4. This triggers a transition to PROCESSING_BOT to handle the finalized user input.

### Benefits of Event-Driven Approach for Barge-In

- **Natural Interruption Model:** Events provide a natural way to model interruptions without complex flags.
- **Decoupled Components:** The `BargeInManager` doesn't need to know about the state machine; it just emits events.
- **Consistent Handling:** All types of interruptions (barge-in, pause detection, errors) are handled through the same event mechanism.
- **Improved Testability:** Event handling can be tested in isolation from the components that emit the events.

This event-driven approach ensures that barge-in and pause detection are always responsive, without introducing complex queuing or race conditions.

---
