# Session State Machine

This module implements a robust state machine for managing session states and transitions.

## Architecture

The state machine follows a pattern where:

1. Each session has a single state at any given time
2. Transitions between states are explicit and validated
3. Actions can be executed on state entry and exit
4. Metadata is passed between components in a standardized format

## Key Components

### EnhancedSessionStateManager

The core state manager that handles state transitions and action execution.

- Maintains the current state
- Validates state transitions
- Executes entry and exit actions
- Tracks state history and durations
- Prevents infinite loops and recursion

### StateTransitionMetadata

A standardized structure for metadata passed during state transitions.

- Ensures consistent metadata format
- Provides type safety for known properties
- Includes validation and standardization functions

### State Transitions

Explicit definitions of valid state transitions with validation rules.

- Defines all valid transitions between states
- Includes descriptions for each transition
- Validates metadata requirements for specific transitions
- Provides detailed error messages for invalid transitions

### StateActions

Actions that are executed when entering or exiting states.

- Each action implements the `StateAction` interface
- Actions receive context including the state and metadata
- Actions can perform side effects like processing input or sending responses

## State Transition Flow

1. A component calls `setState(newState, metadata)` on the state manager
2. The state manager checks if another transition is already in progress
3. The state manager standardizes and validates the metadata
4. The state manager validates the transition against the transition graph
5. The state manager executes exit actions for the current state
6. The state is updated to the new state
7. The state manager executes entry actions for the new state
8. The state manager cleans up transition flags and resources

## Implementation Progress

### Phase 1: Standardize Metadata Structure ✅

- [x] Define a clear interface for state transition metadata
- [x] Add metadata validation and standardization to the state manager
- [x] Update key parts of the code to use the standardized metadata

### Phase 2: Enhanced State Transition Validation ✅

- [x] Improve the validation logic in the state manager
- [x] Add better logging for invalid transitions
- [x] Make the transition graph more explicit and well-documented

### Phase 3: Simplified Transition Protection ✅

- [x] Add a simple "in transition" flag to prevent overlapping transitions
- [x] Add proper cleanup in try/finally blocks
- [x] Improve error handling for failed transitions

### Additional Improvements ✅

- [x] Remove unused imports to fix linting warnings
- [x] Replace `any` types with proper type definitions in test files
- [x] Add test-specific type definitions to improve type safety

## Best Practices

1. **Always use standardized metadata**: Use the `StateTransitionMetadata` interface for all metadata passed to state transitions.

2. **Validate state transitions**: Make sure transitions are valid before executing them.

3. **Handle errors gracefully**: Use try/finally blocks to ensure proper cleanup even if actions fail.

4. **Document state transitions**: Add clear comments explaining why transitions are happening.

5. **Keep actions focused**: Each action should have a single responsibility.

6. **Follow the transition rules**: Consult the state transition definitions in `state-transitions.ts` when adding new transitions.

7. **Provide detailed metadata**: Include all required metadata properties for specific transitions.

8. **Handle concurrent transitions**: Be aware that transitions can be rejected if another transition is already in progress.

9. **Use emergency transitions sparingly**: Only DISCONNECTING state is allowed to interrupt an ongoing transition.

## Example Usage
## RequestId Generation and Propagation

A unique `requestId` is generated for every user input event (speech, DTMF, etc.) to ensure robust tracking and metrics association throughout the session lifecycle.

- **Where is it generated?**
  The `requestId` is generated in the `StartMetricsRequestAction` as the first action when entering the `PROCESSING_INPUT` state. This is done via `metricsAdapter.startRequest(userInput)`, which creates and stores a new `requestId` in the session/request context.

  **Contract:** After starting a new request, the session context is always updated via `session.setCurrentRequest(newContext)`, where `newContext` is the value returned by `metricsAdapter.startRequest`. All downstream actions and event payloads must access the current request context (and its `requestId`) via `session.getCurrentRequest()`. This ensures the correct `requestId` is always available and propagated throughout the session and metrics flow.

- **How is it propagated?**
  All subsequent actions, events, and metrics for that user input must propagate the same `requestId`. It is required in all event payloads and metrics calls.

- **Why is this important?**
  This guarantees that every user input is uniquely tracked from the very start, and all downstream events and metrics are correctly associated with the same `requestId`. The metrics system enforces this at runtime, throwing an error if a phase is started without a valid `requestId`.

- **Best practice:**
  Never emit an event or start a metrics phase for a user input without a valid `requestId`. Always use the session/request context to access and propagate the current `requestId`.

See [Monitoring: RequestId Lifecycle](../../services/monitoring/request-id-lifecycle.md) for details.

```typescript
// Transition to a new state with standardized metadata
await stateManager.setState(SessionState.PROCESSING_INPUT, {
  reason: 'User provided speech input',
  inputType: 'speech',
  transcript: userTranscript,
});

// Execute exit actions without changing state
await stateManager.executeExitActionsForCurrentState({
  reason: 'Processing user input',
  inputType: 'speech',
  transcript: userTranscript,
});

// Example of a transition with specific metadata requirements
await stateManager.setState(SessionState.PROCESSING_BOT, {
  reason: 'Initial bot greeting',
  isInitialGreeting: true, // Required for this specific transition
});

// Handle potential concurrent transition errors
try {
  await stateManager.setState(SessionState.PROCESSING_INPUT, {
    reason: 'User provided speech input',
    inputType: 'speech',
    transcript: userTranscript,
  });
} catch (error) {
  if (error.message.includes('another transition is in progress')) {
    // Handle concurrent transition error
    console.log('Cannot transition now, another transition is in progress');
  } else {
    // Handle other errors
    console.error('Error during state transition:', error);
  }
}
```
