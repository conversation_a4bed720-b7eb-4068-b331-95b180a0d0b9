import { SessionState } from '../../session-state-manager';
import { DisposeResourcesAction } from '../actions/common/dispose-resources-action';
import { createMockSession, createMockStateContext } from './test-utils';

// Mock the logging module
jest.mock('../../../services/monitoring/logging', () => ({
  logInfo: jest.fn(),
  logError: jest.fn(),
  logDebug: jest.fn(),
  logWarning: jest.fn(),
  logStateTransition: jest.fn(),
  prepareMetadataForLogging: jest.fn().mockImplementation(metadata => metadata),
}));

describe('DISCONNECTING and CLOSED state actions', () => {
  let session: ReturnType<typeof createMockSession>;
  let context: ReturnType<typeof createMockStateContext>;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Create session and context
    session = createMockSession();
    (session.getSessionState as jest.Mock).mockReturnValue(SessionState.DISCONNECTING);

    // Override mock methods
    session.disposeAudio = jest.fn().mockResolvedValue(undefined);
    session.disposeBot = jest.fn().mockResolvedValue(undefined);
    session.closeWebSocket = jest.fn();
    session.finalizeMetrics = jest.fn();

    // Create context with the session
    context = createMockStateContext(SessionState.DISCONNECTING, {}, session);
  });

  describe('DisposeResourcesAction', () => {
    it('calls all cleanup methods on session', async () => {
      const action = new DisposeResourcesAction();
      await action.execute(context);
      expect(session.disposeAudio).toHaveBeenCalled();
      expect(session.disposeBot).toHaveBeenCalled();
      expect(session.closeWebSocket).toHaveBeenCalled();
      expect(session.finalizeMetrics).toHaveBeenCalled();
    });
  });

  // CLOSED state is just logging, covered by LogStateTransitionAction tests elsewhere
});
