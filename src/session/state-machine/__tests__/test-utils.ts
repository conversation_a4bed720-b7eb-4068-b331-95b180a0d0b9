/**
 * Test utilities for state machine tests.
 * Provides helper functions for creating mock objects.
 */

import type { ISession } from '../../session-interface';
import { SessionState } from '../../session-state-manager';
import type { StateAction, StateContext } from '../interfaces';
import type { PlaybackState } from '../../../services/barge-in/barge-in-manager';
import type { ServerMessage } from '../../../protocol/message';
import type { BotTurnDisposition } from '../../../protocol/voice-bots';
import { TestBotResponse, TestTTSService } from './test-types';
import { StateTransitionMetadata } from '../types/state-metadata';
import { EventEmitter } from '../../../services/events/event-emitter';

/**
 * Create a mock session for testing.
 * @returns A mock session implementing the ISession interface
 */
export function createMockSession(): ISession {
  return {
    id: 'test-session-id',
    getSessionState: jest.fn().mockReturnValue(SessionState.INITIALIZING),
    getStartTime: jest.fn().mockReturnValue(new Date()),
    setPlaybackState: jest.fn(),
    updatePlaybackStateWithoutTransition: jest.fn(),
    enableBargeIn: jest.fn(),
    disableBargeIn: jest.fn(),
    isBargeInEnabled: jest.fn().mockReturnValue(true),
    initializeASRService: jest.fn().mockResolvedValue(undefined),
    processBotStart: jest
      .fn()
      .mockImplementation((isInitialGreeting?: boolean) => Promise.resolve()),
    getConversationId: jest.fn().mockReturnValue('test-conversation-id'),
    setConversationId: jest.fn(),
    setClientAni: jest.fn(),
    close: jest.fn(),
    send: jest.fn(),
    sendBotTurnResponse: jest.fn(),
    processBinaryMessage: jest.fn().mockResolvedValue(undefined),
    getSessionStateManager: jest.fn(),
    getMetricsAdapter: jest.fn(),
    getBotAdapter: jest.fn(),
    getCurrentRequest: jest.fn(),
    // Additional methods required by ISession interface
    getStartMetricsRequest: jest.fn(),
    getLatestBotResponse: jest.fn(),
    setLatestBotResponse: jest.fn(),
    sendAudio: jest.fn().mockResolvedValue(undefined),
    getTTSService: jest.fn(),
    disposeAudio: jest.fn().mockResolvedValue(undefined),
    disposeBot: jest.fn().mockResolvedValue(undefined),
    closeWebSocket: jest.fn(),
    finalizeMetrics: jest.fn(),
    prepareForUserInput: jest.fn().mockResolvedValue(undefined),
    getEventEmitter: jest.fn().mockReturnValue(new EventEmitter()),
  };
}

/**
 * Create a mock state context for testing.
 * @param state The session state
 * @param metadata Optional metadata
 * @param sessionOverrides Optional session method overrides
 * @returns A mock state context
 */
export function createMockStateContext(
  state: SessionState = SessionState.INITIALIZING,
  metadata: Partial<StateTransitionMetadata> = {},
  sessionOverrides: Partial<ISession> = {}
): StateContext {
  // Ensure metadata has a reason if not provided
  const standardizedMetadata: StateTransitionMetadata = {
    reason: 'Test reason',
    ...metadata,
  };

  return {
    state,
    metadata: standardizedMetadata,
    session: {
      ...createMockSession(),
      ...sessionOverrides,
    },
  };
}

/**
 * Create a mock state action for testing.
 * @returns A mock state action
 */
export function createMockAction(): StateAction {
  return {
    execute: jest.fn().mockResolvedValue(undefined),
  };
}
