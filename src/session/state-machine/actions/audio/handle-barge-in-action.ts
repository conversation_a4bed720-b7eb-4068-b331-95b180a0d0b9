/**
 * HandleBargeInAction
 *
 * Action to handle barge-in events in a standardized way.
 * This action centralizes the logic for handling barge-in events
 * and ensures consistent behavior regardless of which state the barge-in occurs in.
 *
 * @module session/state-machine/actions/audio/handle-barge-in-action
 */

import type { StateAction, StateContext } from '../../interfaces';
import { SessionState } from '../../../session-state-manager';
import { logInfo, logWarning } from '../../../../services/monitoring/logging';

export class HandleBargeInAction implements StateAction {
  /**
   * Executes the barge-in handling logic.
   * @param context The state context (must provide session and metadata)
   */
  async execute(context: StateContext): Promise<void> {
    const { session, metadata, state: currentState, nextState } = context;

    // Check if this is actually a barge-in event
    if (!metadata?.bargeInSource) {
      // This is not a barge-in event, so we don't need to do anything
      return;
    }

    const source = metadata.bargeInSource as 'speech' | 'dtmf';
    const text = metadata.bargeInText as string | undefined;

    logInfo(`[HandleBargeInAction] Processing barge-in from ${source}${text ? `: "${text}"` : ''}`);

    // Get the bot adapter to cancel any ongoing bot turn
    const botAdapter = session.getBotAdapter();
    if (botAdapter) {
      botAdapter.cancelCurrentTurn();
      logInfo('[HandleBargeInAction] Canceled current bot turn');
    } else {
      logWarning('[HandleBargeInAction] No bot adapter available, cannot cancel current turn');
    }

    // Removed endPhase('textToSpeech') and endPhase('llmProcessing'); now handled at true async boundaries in the service layer.

    // Cancel any ongoing asynchronous bot processing
    if (
      typeof session.getCurrentProcessingAbortController === 'function' &&
      typeof session.setCurrentProcessingAbortController === 'function'
    ) {
      const abortController = session.getCurrentProcessingAbortController();
      if (abortController) {
        logInfo('[HandleBargeInAction] Cancelling ongoing asynchronous bot processing');
        abortController.abort();
        session.setCurrentProcessingAbortController(null);
      }
    }

    // Get the state manager for state transitions
    const stateManager = session.getSessionStateManager();

    // Check if we're already transitioning to the appropriate state
    // This prevents infinite loops when the Session.handleBargeIn method
    // has already initiated the state transition
    if (currentState === SessionState.RESPONDING && nextState === SessionState.PROCESSING_INPUT) {
      logInfo(
        '[HandleBargeInAction] Already transitioning to PROCESSING_INPUT, skipping redundant transition'
      );
      return;
    } else if (currentState === SessionState.PLAYING && nextState === SessionState.IDLE) {
      logInfo('[HandleBargeInAction] Already transitioning to IDLE, skipping redundant transition');
      return;
    }

    // Determine the appropriate next state based on current state
    if (currentState === SessionState.RESPONDING) {
      // During response preparation, transition to PROCESSING_INPUT
      logInfo(
        '[HandleBargeInAction] Barge-in during RESPONDING state, transitioning to PROCESSING_INPUT'
      );
      await stateManager.setState(SessionState.PROCESSING_INPUT, {
        reason: `Barge-in during response preparation from ${source}`,
        inputType: source,
        text,
        isBargeIn: true,
      });
    } else if (currentState === SessionState.PLAYING) {
      // During playback, transition to IDLE
      logInfo('[HandleBargeInAction] Barge-in during PLAYING state, transitioning to IDLE');
      await stateManager.setState(SessionState.IDLE, {
        reason: `Barge-in during playback from ${source}`,
        bargeInSource: source,
        bargeInText: text,
      });
    } else {
      logWarning(`[HandleBargeInAction] Barge-in received in unexpected state: ${currentState}`);
    }
  }
}
