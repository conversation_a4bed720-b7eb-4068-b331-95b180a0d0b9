/**
 * Action to start playback (TTS or audio) for the RESPONDING state.
 * Intended for use as an entry action for the RESPONDING state.
 *
 * This action now handles barge-in configuration based on metadata.
 *
 * @module session/state-machine/actions/audio/start-playback-action
 */

import type { StateAction, StateContext } from '../../interfaces';

export class StartPlaybackAction implements StateAction {
  async execute(context: StateContext): Promise<void> {
    const { session, metadata } = context;
    const botResponse = session.getLatestBotResponse();

    // Import logging functions
    const { logInfo, logError, logWarning } = await import('../../../../services/logging/logging');

    // Check if we have a bot response
    if (!botResponse) {
      logWarning('[StartPlaybackAction] No bot response available, cannot start playback');

      // This might happen if the asynchronous bot processing hasn't completed yet
      // We'll set a flag on the session to indicate that we need to retry playback
      // when the bot response becomes available
      logInfo('[StartPlaybackAction] This is likely due to asynchronous bot processing');
      logInfo(
        '[StartPlaybackAction] The BOT_RESPONSE_RECEIVED event will trigger refreshResponse when available'
      );

      return;
    }

    try {
      // Removed textToSpeech phase timing; now handled at true async boundary in audio-service-adapter.ts

      // Configure barge-in based on metadata
      const bargeInEnabled = metadata?.bargeInEnabled !== false; // Enable by default unless explicitly disabled

      if (bargeInEnabled) {
        session.enableBargeIn();
        logInfo('[StartPlaybackAction] Barge-in detection enabled for this playback');
      } else {
        session.disableBargeIn();
        logInfo('[StartPlaybackAction] Barge-in detection disabled for this playback');
      }

      // Log that we're starting playback with detailed information
      logInfo('[StartPlaybackAction] ===== STARTING PLAYBACK OF BOT RESPONSE =====');
      logInfo(
        `[StartPlaybackAction] Bot response text: "${botResponse.text?.substring(0, 50)}${
          botResponse.text?.length > 50 ? '...' : ''
        }"`
      );
      logInfo(`[StartPlaybackAction] Bot response has audio: ${!!botResponse.audioBytes}`);
      if (botResponse.audioBytes) {
        logInfo(`[StartPlaybackAction] Audio size: ${botResponse.audioBytes.length} bytes`);
      }

      // Send audio to the client
      if (botResponse.audioBytes) {
        logInfo(
          `[StartPlaybackAction] Sending ${botResponse.audioBytes.length} bytes of pre-generated audio`
        );
        try {
          await session.sendAudio(botResponse.audioBytes, session.getCurrentRequest?.());
          logInfo('[StartPlaybackAction] Successfully sent pre-generated audio to client');
          // End the textToSpeech phase after sending audio
          // Removed textToSpeech phase timing; now handled at true async boundary in audio-service-adapter.ts
        } catch (audioError) {
          logError(
            `[StartPlaybackAction] Error sending pre-generated audio: ${
              audioError instanceof Error ? audioError.message : String(audioError)
            }`
          );
          throw audioError; // Re-throw to be caught by outer try/catch
        }
      } else if (botResponse.text) {
        const ttsService = session.getTTSService();
        if (ttsService && typeof ttsService.getAudioBytes === 'function') {
          logInfo(
            `[StartPlaybackAction] Generating audio for text: "${botResponse.text.substring(
              0,
              50
            )}${botResponse.text.length > 50 ? '...' : ''}"`
          );
          try {
            const audioBytes = await ttsService.getAudioBytes(botResponse.text);
            logInfo(
              `[StartPlaybackAction] Successfully generated ${audioBytes.length} bytes of audio`
            );

            // Send the generated audio
            logInfo(
              `[StartPlaybackAction] Sending ${audioBytes.length} bytes of generated audio to client`
            );
            await session.sendAudio(audioBytes, session.getCurrentRequest?.());
            logInfo('[StartPlaybackAction] Successfully sent generated audio to client');
            // End the textToSpeech phase after sending generated audio
            // Removed textToSpeech phase timing; now handled at true async boundary in audio-service-adapter.ts
          } catch (ttsError) {
            logError(
              `[StartPlaybackAction] Error generating or sending TTS audio: ${
                ttsError instanceof Error ? ttsError.message : String(ttsError)
              }`
            );
            throw ttsError; // Re-throw to be caught by outer try/catch
          }
        } else {
          logWarning('[StartPlaybackAction] No TTS service available to generate audio');
        }
      } else {
        logWarning(
          '[StartPlaybackAction] Bot response has neither text nor audio, cannot play anything'
        );
      }

      // Set playback state
      if (typeof session.setPlaybackState === 'function') {
        logInfo('[StartPlaybackAction] Setting playback state to playing');

        // Get the state manager to check if a transition is in progress
        const stateManager = session.getSessionStateManager();

        if (
          stateManager &&
          typeof stateManager.isStateTransitionInProgress === 'function' &&
          stateManager.isStateTransitionInProgress()
        ) {
          // A transition is in progress, so we'll log a warning and rely on the client's playback-started message
          logInfo('[StartPlaybackAction] Transition in progress, will not set playback state now');
          logInfo('[StartPlaybackAction] Will wait for current transition to complete');

          // We won't try to transition to PLAYING state here
          // Instead, we'll rely on the client's playback-started message or the AudioServiceAdapter
          logInfo(
            '[StartPlaybackAction] Will rely on AudioServiceAdapter or client message for state transition'
          );
        } else {
          // No transition in progress, so we can set the playback state
          session.setPlaybackState('playing');
        }
      } else {
        logWarning('[StartPlaybackAction] Session does not have setPlaybackState method');
      }

      // No longer attempting to transition to PLAYING state here
      // Instead, we'll wait for the client to send a "playback-started" message
      // which will be handled by PlaybackStartedMessageHandler
      logInfo('[StartPlaybackAction] Audio sent to client, waiting for playback-started message');

      // Note: The transition to PLAYING state will be handled by PlaybackStartedMessageHandler
      // when the client confirms that playback has started

      logInfo('[StartPlaybackAction] ===== PLAYBACK SETUP COMPLETE =====');
    } catch (error) {
      logError(
        `[StartPlaybackAction] Error starting playback: ${
          error instanceof Error ? error.message : String(error)
        }`
      );

      // No longer attempting emergency transition to PLAYING state
      // Instead, we'll log the error and let the client handle the transition
      logError(
        '[StartPlaybackAction] Error during audio playback setup, client will need to retry'
      );
    }
  }
}
