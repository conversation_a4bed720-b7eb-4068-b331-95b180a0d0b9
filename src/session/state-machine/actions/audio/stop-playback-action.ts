/**
 * Action to stop playback when exiting the PLAYING state.
 * Intended for use as an exit action for the PLAYING state.
 *
 * This action handles cleanup tasks when exiting the PLAYING state:
 * - Clears the latest bot response to prevent repeated playback
 * - Sets the playback state to stopped
 * - Logs the reason for stopping playback
 *
 * Note: This action does NOT transition to IDLE state. The transition
 * is handled by the PlaybackCompletedMessageHandler when the client
 * reports playback completion.
 *
 * @module session/state-machine/actions/audio/stop-playback-action
 */

import type { StateAction, StateContext } from '../../interfaces';

export class StopPlaybackAction implements StateAction {
  async execute(context: StateContext): Promise<void> {
    const { session, metadata } = context;

    // Import logging functions
    const { logInfo } = await import('../../../../services/monitoring/logging');

    // Clear the latest bot response to prevent repeated playback
    if (typeof session.setLatestBotResponse === 'function') {
      session.setLatestBotResponse(undefined);
      logInfo('[StopPlaybackAction] Cleared latest bot response');
    }

    // Set playback state to stopped
    if (typeof session.setPlaybackState === 'function') {
      session.setPlaybackState('stopped');
      logInfo('[StopPlaybackAction] Set playback state to stopped');
    }

    // Log the reason for stopping playback
    const reason = metadata?.reason || 'Unknown reason';
    const bargeIn = metadata?.isBargeIn === true;

    if (bargeIn) {
      logInfo(`[StopPlaybackAction] Playback stopped due to barge-in: ${reason}`);
    } else {
      logInfo(`[StopPlaybackAction] Playback stopped: ${reason}`);
    }
  }
}
