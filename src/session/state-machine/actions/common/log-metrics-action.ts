/**
 * Action to log metrics for state transitions.
 * Intended for use as an entry action for states that should be tracked in metrics.
 *
 * @module session/state-machine/actions/common/log-metrics-action
 */

import { logMetrics } from '../../../../services/logging/logging';
import type { StateAction, StateContext } from '../../interfaces';

/**
 * Logs metrics information for important state transitions.
 * This is useful for tracking performance and behavior patterns.
 */
export class LogMetricsAction implements StateAction {
  /**
   * Executes the metrics logging action on state entry.
   * @param context The state context for this transition
   */
  async execute(context: StateContext): Promise<void> {
    const metadataStr = context.metadata ? ` | metadata: ${JSON.stringify(context.metadata)}` : '';
    logMetrics(
      `[SessionStateMachine] Session ${context.session.id} state: ${context.state}${metadataStr}`
    );
  }
}
