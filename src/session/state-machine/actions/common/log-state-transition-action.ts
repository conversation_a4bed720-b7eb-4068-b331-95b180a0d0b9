import { logDebug, prepareMetadataForLogging } from '../../../../services/monitoring/logging';
import type { StateAction, StateContext } from '../../interfaces';

/**
 * Logs the state transition using the project's logging system.
 * Useful for debugging and auditing state changes.
 */
export class LogStateTransitionAction implements StateAction {
  /**
   * Executes the logging action on state entry or exit.
   * @param context The state context for this transition
   */
  async execute(context: StateContext): Promise<void> {
    const loggableMetadata = prepareMetadataForLogging(context.metadata);
    logDebug(
      `[SessionStateMachine] Session ${context.session.id} transitioned to state: ${context.state}` +
        (loggableMetadata ? ` | metadata: ${JSON.stringify(loggableMetadata)} FF` : '')
    );
  }
}
