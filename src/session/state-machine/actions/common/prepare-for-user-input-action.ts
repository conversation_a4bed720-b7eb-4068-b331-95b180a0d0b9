/**
 * Action to prepare the session for new user input (e.g., after playback).
 * Intended for use as an entry action for the IDLE state.
 *
 * @module session/state-machine/actions/common/prepare-for-user-input-action
 */

import type { StateAction, StateContext } from '../../interfaces';

export class PrepareForUserInputAction implements StateAction {
  async execute(context: StateContext): Promise<void> {
    await context.session.prepareForUserInput();
  }
}
