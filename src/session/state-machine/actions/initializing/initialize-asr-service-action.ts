import type { StateAction, StateContext } from '../../interfaces';
import { logInfo, logWarning } from '../../../../services/monitoring/logging';

/**
 * Action to initialize the ASR service for the session.
 * Intended for use as an entry action for the INITIALIZING state.
 */
export class InitializeASRServiceAction implements StateAction {
  /**
   * Executes the ASR initialization logic.
   * @param context The state context (must provide session and metadata with conversationId)
   */
  async execute(context: StateContext): Promise<void> {
    // Check if conversationId is in metadata
    const conversationId = context.metadata?.conversationId as string;

    // If no conversationId in metadata, check if it's already set on the session
    if (!conversationId) {
      // Get the conversationId from the session if available
      const sessionConversationId = context.session.getConversationId();

      if (sessionConversationId) {
        logInfo(
          `[InitializeASRServiceAction] Using existing conversation ID: ${sessionConversationId}`
        );
        await context.session.initializeASRService(sessionConversationId);
        return;
      }

      // If no conversationId is available, log a warning but don't throw an error
      // This allows the session to continue initializing
      logWarning(
        '[InitializeASRServiceAction] No conversation ID available in metadata or session'
      );
      return;
    }

    // Initialize ASR service with the conversation ID from metadata
    await context.session.initializeASRService(conversationId);
  }
}
