import type { StateAction, StateContext } from '../../interfaces';
import { logDebug, logInfo } from '../../../../services/monitoring/logging';
import { SessionState } from '../../../session-state-manager';

/**
 * Action to process the bot start for the session.
 * Intended for use as an entry action for the PROCESSING_BOT state.
 *
 * For initial greetings: Calls the public processBotStart() method on the session.
 * For regular input: Verifies that a bot response is available or being processed asynchronously.
 */
export class ProcessBotStartAction implements StateAction {
  /**
   * Executes the bot start logic.
   * @param context The state context (must provide session)
   */
  async execute(context: StateContext): Promise<void> {
    const { isInitialGreeting, reason } = context.metadata || {};

    // Skip if this is the initial greeting - it's handled by BotServiceAdapter.getInitialResponse
    if (isInitialGreeting || reason === 'Processing initial bot greeting') {
      logDebug('[ProcessBotStartAction] Skipping for initial greeting (already handled)');
      return;
    }

    // For regular bot processing, check if we already have a bot response
    const botResponse = context.session.getLatestBotResponse();

    if (botResponse) {
      logInfo(
        `[ProcessBotStartAction] Bot response already available (${
          botResponse.text?.length || 0
        } chars, ${botResponse.audioBytes?.length || 0} bytes audio)`
      );
      // No need to call processBotStart since the response is already processed
      return;
    }

    // If no bot response is available yet, it's likely being processed asynchronously
    logInfo(
      '[ProcessBotStartAction] No bot response available yet. Waiting for asynchronous processing to complete.'
    );

    // We don't need to do anything here - the asynchronous processing will emit the BOT_RESPONSE_RECEIVED event
    // when it completes, which will trigger the transition to RESPONDING state.

    // Set a timeout to log a warning if the bot response takes too long
    setTimeout(() => {
      // Only log if we're still in PROCESSING_BOT state
      if (context.session.getSessionStateManager().getState() === SessionState.PROCESSING_BOT) {
        logInfo(
          '[ProcessBotStartAction] Still waiting for bot response after 5 seconds. This may indicate a processing delay.'
        );
      }
    }, 5000);
  }
}
