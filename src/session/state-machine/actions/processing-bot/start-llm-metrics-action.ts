/**
 * Action to start LLM processing metrics for the PROCESSING_BOT state.
 * Intended for use as an entry action for the PROCESSING_BOT state.
 *
 * @module session/state-machine/actions/processing-bot/start-llm-metrics-action
 */

import type { StateAction, StateContext } from '../../interfaces';

export class StartLLMMetricsAction implements StateAction {
  async execute(context: StateContext): Promise<void> {
    const { session } = context;
    const metricsAdapter = session.getMetricsAdapter?.();
    if (!metricsAdapter) {
      throw new Error(
        'StartLLMMetricsAction requires session.getMetricsAdapter() to return a metrics adapter'
      );
    }
    // Removed: LLM phase metrics should be started at the true async boundary in the bot service, not in the state machine.
  }
}
