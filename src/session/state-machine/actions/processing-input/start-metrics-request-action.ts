/**
 * Action to start metrics tracking for the current request.
 * Intended for use as an entry action for the PROCESSING_INPUT state.
 *
 * @module session/state-machine/actions/processing-input/start-metrics-request-action
 */

import type { StateAction, StateContext } from '../../interfaces';
import type { ISession } from '../../../session-interface';
import { logInfo } from '../../../../services/logging/logging';

/**
 * Action to start metrics tracking for the current request.
 * Expects context.metadata to contain one of:
 *   - reason: 'Processing initial connection' (for initial bot greeting)
 *   - inputType: 'speech' or 'dtmf' with appropriate data
 */
export class StartMetricsRequestAction implements StateAction {
  async execute(context: StateContext): Promise<void> {
    const { inputType, transcript, digits, reason } = context.metadata || {};
    const session = context.session as ISession;

    // Get the metrics adapter from the session
    const metricsAdapter = session.getMetricsAdapter();
    if (!metricsAdapter) {
      throw new Error(
        'StartMetricsRequestAction requires session.getMetricsAdapter() to return a metrics adapter'
      );
    }

    // Special case for initial connection
    if (reason === 'Processing initial connection') {
      logInfo('[StartMetricsRequestAction] Starting metrics for initial connection');
      const newContext = await metricsAdapter.startRequest('Initial greeting');
      session.setCurrentRequest(newContext);
      return;
    }

    // Regular case - require inputType
    if (!inputType) {
      throw new Error(
        'StartMetricsRequestAction requires inputType in metadata for regular requests'
      );
    }

    let userInput: string;

    if (inputType === 'speech') {
      if (!transcript) {
        throw new Error(
          'StartMetricsRequestAction requires transcript in metadata for speech input'
        );
      }
      userInput =
        typeof transcript === 'string' ? transcript : (transcript as { text: string }).text;
    } else if (inputType === 'dtmf') {
      if (!digits) {
        throw new Error('StartMetricsRequestAction requires digits in metadata for dtmf input');
      }
      userInput = digits as string;
    } else {
      throw new Error(`StartMetricsRequestAction: Unsupported inputType: ${inputType}`);
    }

    // Start metrics tracking using the adapter
    logInfo(`[StartMetricsRequestAction] Starting metrics for ${inputType} input: "${userInput}"`);
    const newContext = await metricsAdapter.startRequest(userInput);
    session.setCurrentRequest(newContext);
  }
}
