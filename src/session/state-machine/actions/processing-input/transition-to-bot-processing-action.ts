/**
 * Action to transition from PROCESSING_INPUT to PROCESSING_BOT state.
 * Intended for use as an exit action for the PROCESSING_INPUT state.
 * This action emits a BOT_PROCESSING_REQUESTED event instead of returning a StateTransitionRequest.
 *
 * @module session/state-machine/actions/processing-input/transition-to-bot-processing-action
 */

// import { SessionState } from '../../../session-state-manager'; // Unused import removed
import type { StateAction, StateContext } from '../../interfaces';
import {
  logDebug,
  logWarning,
  logInfo,
  prepareMetadataForLogging,
} from '../../../../services/logging/logging';
import type { Transcript } from '../../../../services/speech/base/types';
import { EventEmitter, EventPriority } from '../../../../services/events/event-emitter';
import {
  SessionEventType,
  BotProcessingRequestedEventData,
} from '../../../../services/events/session-events';

/**
 * Action to transition from PROCESSING_INPUT to PROCESSING_BOT state.
 * This action should be executed after processing user input.
 */
export class TransitionToBotProcessingAction implements StateAction {
  private eventEmitter: EventEmitter;

  constructor(options?: { eventEmitter?: EventEmitter }) {
    // Get the event emitter from options or create a new one
    this.eventEmitter = options?.eventEmitter || new EventEmitter();
  }

  async execute(context: StateContext): Promise<void> {
    // Log the metadata object (excluding binary data)
    const loggableMetadata = prepareMetadataForLogging(context.metadata);
    logInfo(
      `[TransitionToBotProcessingAction] EXECUTING with context state: ${context.state}, next state: ${context.nextState}`
    );
    logInfo(
      `[TransitionToBotProcessingAction] Received context metadata: ${JSON.stringify(
        loggableMetadata
      )}`
    );

    // Get necessary objects from context
    const { reason, isInitialGreeting, inputType, transcript } = context.metadata || {};

    // Skip for initial greeting (already handled by session)
    if (isInitialGreeting || reason === 'Processing initial bot greeting') {
      logDebug(
        '[TransitionToBotProcessingAction] Skipping transition for initial greeting (already handled by session)'
      );
      return;
    }

    // Special case for initial connection
    if (reason === 'Processing initial connection') {
      logDebug('[TransitionToBotProcessingAction] Processing initial connection');

      // Emit BOT_PROCESSING_REQUESTED event
      this.eventEmitter.emit(
        SessionEventType.BOT_PROCESSING_REQUESTED,
        {
          isInitialGreeting: true,
          timestamp: Date.now(),
        } as BotProcessingRequestedEventData,
        EventPriority.HIGH
      );

      return;
    }

    // Get necessary services
    const session = context.session;
    const botAdapter = session.getBotAdapter();
    const metricsContext = session.getCurrentRequest();

    // Handle speech input
    if (inputType === 'speech' && transcript) {
      // Get transcript object
      const transcriptObj =
        typeof transcript === 'object' && transcript !== null && 'text' in transcript
          ? (transcript as Transcript)
          : { text: String(transcript), confidence: 0 };

      logDebug(
        `[TransitionToBotProcessingAction] Processing speech input before transitioning: "${transcriptObj.text}"`
      );

      try {
        // Process the user input and wait for the response
        const result = await botAdapter.processUserInput(transcriptObj, metricsContext);

        if (result.success && result.response) {
          logDebug(
            `[TransitionToBotProcessingAction] Received bot response (${
              result.response.text?.length || 0
            } chars, ${result.response.audioBytes?.length || 0} bytes audio)`
          );

          // Set the bot response on the session
          session.setLatestBotResponse(result.response);

          // Emit BOT_PROCESSING_REQUESTED event
          this.eventEmitter.emit(
            SessionEventType.BOT_PROCESSING_REQUESTED,
            {
              input: transcriptObj,
              inputType: 'speech',
              timestamp: Date.now(),
              requestId: metricsContext?.requestId,
            } as BotProcessingRequestedEventData,
            EventPriority.HIGH
          );
        } else {
          logWarning(
            '[TransitionToBotProcessingAction] No bot response received or processing failed, aborting transition'
          );
        }
      } catch (err) {
        logWarning(
          `[TransitionToBotProcessingAction] Error: ${
            err instanceof Error ? err.message : String(err)
          }`
        );
      }
    }
    // Handle non-speech input (e.g., DTMF)
    else if (inputType === 'dtmf') {
      logDebug('[TransitionToBotProcessingAction] Processing DTMF input');

      // Emit BOT_PROCESSING_REQUESTED event
      this.eventEmitter.emit(
        SessionEventType.BOT_PROCESSING_REQUESTED,
        {
          inputType: 'dtmf',
          timestamp: Date.now(),
          requestId: metricsContext?.requestId,
        } as BotProcessingRequestedEventData,
        EventPriority.HIGH
      );
    }
    // If we get here, we don't have a valid input type
    else {
      logWarning(`[TransitionToBotProcessingAction] Unsupported input type: ${inputType}`);
    }
  }
}
