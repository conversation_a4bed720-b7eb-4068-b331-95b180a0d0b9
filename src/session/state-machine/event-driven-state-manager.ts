/**
 * EventDrivenStateManager: State manager that uses events to trigger state transitions.
 *
 * This class replaces the EnhancedSessionStateManager with an event-driven approach.
 *
 * @module session/state-machine/event-driven-state-manager
 */

import { SessionState } from '../session-state-manager';
import type { ISession } from '../session-interface';
import type { StateActions, StateContext } from './interfaces';
import {
  logWarning,
  logDebug,
  logStateTransition,
  logError,
  logInfo,
  prepareMetadataForLogging,
} from '../../services/monitoring/logging';
import { StateTransitionMetadata, standardizeMetadata } from './types/state-metadata';
import { validateTransition, findTransitionDefinition } from './types/state-transitions';
import { EventEmitter, EventPriority, Subscription } from '../../services/events/event-emitter';
import { registerStateActions } from './register-actions';
import {
  SessionEventType,
  StateChangedEventData,
  UserInputReceivedEventData,
  BotResponseReceivedEventData,
  PlaybackCompletedEventData,
  BargeInDetectedEventData,
  SessionCloseRequestedEventData,
  InitialGreetingRequestedEventData,
  UserInputProcessedEventData,
  TranscriptReceivedEventData,
  PauseDetectedEventData,
  DtmfSequenceCompletedEventData,
  SessionInitializedEventData,
  BotProcessingRequestedEventData,
  InterruptionRequestedEventData,
  ProcessingInputCompletedEventData,
} from '../../services/events/session-events';
import type { Transcript } from '../../services/speech/base/types';

/**
 * Minimal async mutex for serializing state transitions.
 */
class Mutex {
  private queue: (() => void)[] = [];
  private locked = false;

  async run<T>(fn: () => Promise<T>): Promise<T> {
    await this.acquire();
    try {
      return await fn();
    } finally {
      this.release();
    }
  }

  private acquire(): Promise<void> {
    if (!this.locked) {
      this.locked = true;
      return Promise.resolve();
    }
    return new Promise(resolve => this.queue.push(resolve));
  }

  private release() {
    if (this.queue.length > 0) {
      const next = this.queue.shift();
      next && next();
    } else {
      this.locked = false;
    }
  }
}
export type StateChangeListener = (
  oldState: SessionState,
  newState: SessionState,
  metadata?: StateTransitionMetadata
) => void;

export interface StateTransition {
  from: SessionState;
  to: SessionState;
  timestamp: number;
  reason?: string;
}

export class EventDrivenStateManager {
  private state: SessionState = SessionState.INITIALIZING;
  private listeners: StateChangeListener[] = [];
  private stateHistory: StateTransition[] = [];
  private stateTimestamps: Record<SessionState, number[]> = Object.values(SessionState).reduce(
    (acc, state) => ({ ...acc, [state]: [] }),
    {} as Record<SessionState, number[]>
  );
  private stateActions: Record<SessionState, StateActions> = {} as Record<
    SessionState,
    StateActions
  >;
  private session: ISession;
  private eventEmitter: EventEmitter;
  private subscriptions: Subscription[] = [];

  // Mutex for serializing transitions
  private transitionMutex = new Mutex();

  // Flag to track if a state transition is in progress
  private isTransitionInProgress = false;

  // Store transition information in a single object
  private lastTransition: {
    from: SessionState;
    to: SessionState;
    metadata?: Record<string, unknown>;
    timestamp: number;
    inProgress: boolean;
    reason?: string;
  } | null = null;

  constructor(session: ISession, eventEmitter: EventEmitter) {
    this.session = session;
    this.eventEmitter = eventEmitter;

    // Initialize state actions
    Object.values(SessionState).forEach(state => {
      this.stateActions[state] = { onEnter: [], onExit: [] };
    });

    // Register state actions with the event emitter
    // This is imported from register-actions.ts
    registerStateActions(this, this.eventEmitter);

    // Subscribe to our own STATE_CHANGED event to update lastTransition
    this.subscriptions.push(
      this.eventEmitter.on(SessionEventType.STATE_CHANGED, (_data: StateChangedEventData) => {
        // Update lastTransition with completed transition info
        if (this.lastTransition && this.lastTransition.inProgress) {
          this.lastTransition.inProgress = false;
        }
      })
    );

    // Subscribe to events that should trigger state transitions
    this.subscribeToEvents();
  }

  /**
   * Subscribe to events that should trigger state transitions
   */
  private subscribeToEvents(): void {
    // Session lifecycle events
    this.subscriptions.push(
      this.eventEmitter.on(
        SessionEventType.SESSION_INITIALIZED,
        (data: SessionInitializedEventData) => this.handleSessionInitialized(data)
      )
    );

    this.subscriptions.push(
      this.eventEmitter.on(
        SessionEventType.SESSION_CLOSE_REQUESTED,
        (data: SessionCloseRequestedEventData) => this.handleSessionCloseRequested(data)
      )
    );

    // User input events
    this.subscriptions.push(
      this.eventEmitter.on(
        SessionEventType.USER_INPUT_RECEIVED,
        (data: UserInputReceivedEventData) => this.handleUserInputReceived(data)
      )
    );

    this.subscriptions.push(
      this.eventEmitter.on(
        SessionEventType.USER_INPUT_PROCESSED,
        (data: UserInputProcessedEventData) => this.handleUserInputProcessed(data)
      )
    );

    // Bot events
    this.subscriptions.push(
      this.eventEmitter.on(
        SessionEventType.INITIAL_GREETING_REQUESTED,
        (data: InitialGreetingRequestedEventData) => this.handleInitialGreetingRequested(data)
      )
    );

    this.subscriptions.push(
      this.eventEmitter.on(
        SessionEventType.BOT_RESPONSE_RECEIVED,
        (data: BotResponseReceivedEventData) => this.handleBotResponseReceived(data)
      )
    );

    this.subscriptions.push(
      this.eventEmitter.on(
        SessionEventType.BOT_PROCESSING_REQUESTED,
        (data: BotProcessingRequestedEventData) => this.handleBotProcessingRequested(data)
      )
    );

    // Playback events
    this.subscriptions.push(
      this.eventEmitter.on(
        SessionEventType.PLAYBACK_COMPLETED,
        (data: PlaybackCompletedEventData) => this.handlePlaybackCompleted(data)
      )
    );

    // Barge-in events
    this.subscriptions.push(
      this.eventEmitter.on(SessionEventType.BARGE_IN_DETECTED, (data: BargeInDetectedEventData) =>
        this.handleBargeInDetected(data)
      )
    );

    // ASR events
    this.subscriptions.push(
      this.eventEmitter.on(
        SessionEventType.TRANSCRIPT_RECEIVED,
        (data: TranscriptReceivedEventData) => this.handleTranscriptReceived(data)
      )
    );

    this.subscriptions.push(
      this.eventEmitter.on(SessionEventType.PAUSE_DETECTED, (data: PauseDetectedEventData) =>
        this.handlePauseDetected(data)
      )
    );

    // DTMF events
    this.subscriptions.push(
      this.eventEmitter.on(
        SessionEventType.DTMF_SEQUENCE_COMPLETED,
        (data: DtmfSequenceCompletedEventData) => this.handleDtmfSequenceCompleted(data)
      )
    );

    // Processing input completed events
    this.subscriptions.push(
      this.eventEmitter.on(
        SessionEventType.PROCESSING_INPUT_COMPLETED,
        (data: ProcessingInputCompletedEventData) => this.handleProcessingInputCompleted(data)
      )
    );

    // Interruption events
    this.subscriptions.push(
      this.eventEmitter.on(
        SessionEventType.INTERRUPTION_REQUESTED,
        (data: InterruptionRequestedEventData) => this.handleInterruptionRequested(data)
      )
    );
  }

  /**
   * Clean up event subscriptions
   */
  public dispose(): void {
    // Unsubscribe from all events
    this.subscriptions.forEach(subscription => subscription.unsubscribe());
    this.subscriptions = [];
  }

  // --- Event handlers ---

  /**
   * Handle SESSION_INITIALIZED event
   */
  private async handleSessionInitialized(data: SessionInitializedEventData): Promise<void> {
    if (this.state === SessionState.INITIALIZING) {
      await this.setState(SessionState.IDLE, {
        reason: 'Session initialization completed',
        sessionId: data.sessionId,
        clientId: data.clientId,
      });
    }
  }

  /**
   * Handle SESSION_CLOSE_REQUESTED event
   */
  private async handleSessionCloseRequested(data: SessionCloseRequestedEventData): Promise<void> {
    // Any state can transition to DISCONNECTING
    await this.setState(SessionState.DISCONNECTING, {
      reason: data.reason,
      code: data.code,
    });
  }

  /**
   * Handle USER_INPUT_RECEIVED event
   */
  private async handleUserInputReceived(data: UserInputReceivedEventData): Promise<void> {
    if (this.state === SessionState.IDLE) {
      await this.setState(SessionState.PROCESSING_INPUT, {
        reason: `${data.inputType} input received`,
        inputType: data.inputType,
        input: data.input,
        isFinal: data.isFinal,
      });
    }
  }

  /**
   * Handle USER_INPUT_PROCESSED event
   */
  private async handleUserInputProcessed(data: UserInputProcessedEventData): Promise<void> {
    if (this.state === SessionState.PROCESSING_INPUT) {
      await this.setState(SessionState.PROCESSING_BOT, {
        reason: `${data.inputType} input processed`,
        inputType: data.inputType,
        input: data.input,
        requestId: data.requestId,
      });
    }
  }

  /**
   * Handle INITIAL_GREETING_REQUESTED event
   */
  private async handleInitialGreetingRequested(
    data: InitialGreetingRequestedEventData
  ): Promise<void> {
    if (this.state === SessionState.IDLE) {
      await this.setState(SessionState.PROCESSING_BOT, {
        reason: 'Initial greeting requested',
        isInitialGreeting: true,
        requestId: data.requestId,
      });
    }
  }

  /**
   * Handle BOT_PROCESSING_REQUESTED event
   */
  private async handleBotProcessingRequested(data: BotProcessingRequestedEventData): Promise<void> {
    if (this.state === SessionState.PROCESSING_INPUT || this.state === SessionState.IDLE) {
      await this.setState(SessionState.PROCESSING_BOT, {
        reason: data.isInitialGreeting
          ? 'Processing initial bot greeting'
          : 'User input processed, transitioning to bot processing',
        inputType: data.inputType,
        input: data.input,
        isInitialGreeting: data.isInitialGreeting,
        requestId: data.requestId,
      });
    }
  }

  /**
   * Handle BOT_RESPONSE_RECEIVED event
   */
  private async handleBotResponseReceived(data: BotResponseReceivedEventData): Promise<void> {
    // Always set the bot response on the session, regardless of current state
    this.session.setLatestBotResponse(data.response);

    // End the llmProcessing phase when the bot response is received
    const metricsAdapter = this.session.getMetricsAdapter?.();
    if (metricsAdapter && metricsAdapter.endPhase) {
      await metricsAdapter.endPhase('llmProcessing');
    }

    // Log detailed information about the bot response
    const { logInfo } = await import('../../services/monitoring/logging');
    logInfo(
      `[EventDrivenStateManager] Received bot response: ${data.response.text?.length || 0} chars, ${
        data.response.audioBytes?.length || 0
      } bytes audio`
    );

    if (data.fromAsyncProcessing) {
      logInfo('[EventDrivenStateManager] This response is from asynchronous processing');
    }

    // Handle based on current state
    if (this.state === SessionState.PROCESSING_BOT) {
      // If we're in PROCESSING_BOT, transition to RESPONDING
      logInfo('[EventDrivenStateManager] In PROCESSING_BOT state, transitioning to RESPONDING');
      await this.setState(SessionState.RESPONDING, {
        reason: 'Bot response received',
        hasText: !!data.response.text,
        hasAudio: !!data.response.audioBytes,
        requestId: data.requestId,
      });
    } else if (this.state === SessionState.RESPONDING) {
      // If we're already in RESPONDING, just update the bot response
      // This handles the case where the async processing completes after we've already transitioned
      logInfo(
        '[EventDrivenStateManager] Already in RESPONDING state, updating bot response without state transition'
      );

      // Trigger a refresh of the response handling
      // This is needed because StartPlaybackAction might have failed due to missing response
      if (typeof this.session.refreshResponse === 'function') {
        this.session.refreshResponse();
      }
    } else {
      // For any other state, log but don't transition
      logInfo(
        `[EventDrivenStateManager] Received bot response while in ${this.state} state, storing response but not transitioning`
      );
    }
  }

  /**
   * Handle PLAYBACK_COMPLETED event
   */
  private async handlePlaybackCompleted(data: PlaybackCompletedEventData): Promise<void> {
    if (this.state === SessionState.PLAYING) {
      await this.setState(SessionState.IDLE, {
        reason: 'Playback completed',
        duration: data.duration,
      });
    }
  }

  /**
   * Handle BARGE_IN_DETECTED event
   */
  private async handleBargeInDetected(data: BargeInDetectedEventData): Promise<void> {
    if (this.state === SessionState.PLAYING) {
      await this.setState(SessionState.PROCESSING_INPUT, {
        reason: `Barge-in detected from ${data.source}`,
        bargeInSource: data.source,
        bargeInText: data.text,
        isBargeIn: true,
      });
    } else if (this.state === SessionState.RESPONDING) {
      await this.setState(SessionState.PROCESSING_INPUT, {
        reason: `Barge-in detected from ${data.source} during response preparation`,
        bargeInSource: data.source,
        bargeInText: data.text,
        isBargeIn: true,
      });
    }
  }

  /**
   * Handle TRANSCRIPT_RECEIVED event
   */
  private async handleTranscriptReceived(data: TranscriptReceivedEventData): Promise<void> {
    // Only process final transcripts that should trigger state transitions
    if (data.isFinal && this.state === SessionState.IDLE) {
      await this.setState(SessionState.PROCESSING_INPUT, {
        reason: 'Final transcript received',
        inputType: 'speech',
        input: data.transcript,
        isFinal: true,
      });
    }
  }

  /**
   * Handle PAUSE_DETECTED event
   */
  private async handlePauseDetected(data: PauseDetectedEventData): Promise<void> {
    if (this.state === SessionState.PROCESSING_INPUT) {
      // Emit a PROCESSING_INPUT_COMPLETED event to signal that processing is complete
      logError(
        `[handlePauseDetected] Emitting PROCESSING_INPUT_COMPLETED | requestId=${
          data.requestId ?? 'unknown'
        } | stack=${new Error().stack}`
      );
      this.eventEmitter.emit(
        SessionEventType.PROCESSING_INPUT_COMPLETED,
        {
          input: data.transcript,
          inputType: 'speech',
          timestamp: Date.now(),
          ...(data.requestId && { requestId: data.requestId }),
          emitter: 'handlePauseDetected',
        },
        EventPriority.HIGH
      );
    }
  }

  /**
   * Handle DTMF_SEQUENCE_COMPLETED event
   */
  private async handleDtmfSequenceCompleted(data: DtmfSequenceCompletedEventData): Promise<void> {
    if (this.state === SessionState.PROCESSING_INPUT) {
      // Emit a PROCESSING_INPUT_COMPLETED event to signal that processing is complete
      logError(
        `[handleDtmfSequenceCompleted] Emitting PROCESSING_INPUT_COMPLETED | requestId=${
          data.requestId ?? 'unknown'
        } | stack=${new Error().stack}`
      );
      this.eventEmitter.emit(
        SessionEventType.PROCESSING_INPUT_COMPLETED,
        {
          input: data.sequence,
          inputType: 'dtmf',
          timestamp: Date.now(),
          ...(data.requestId && { requestId: data.requestId }),
          emitter: 'handleDtmfSequenceCompleted',
        },
        EventPriority.HIGH
      );
    }
  }

  /**
   * Handle PROCESSING_INPUT_COMPLETED event
   * This is the clean event-driven approach to handle state transitions
   */
  private async handleProcessingInputCompleted(
    data: ProcessingInputCompletedEventData
  ): Promise<void> {
    logError(
      `[handleProcessingInputCompleted] Handling PROCESSING_INPUT_COMPLETED | currentState=${
        this.state
      } | requestId=${(data as any)?.requestId} | emitter=${(data as any)?.emitter} | stack=${
        new Error().stack
      }`
    );
    if (this.state === SessionState.PROCESSING_INPUT) {
      logDebug(
        '[EventDrivenStateManager] Processing input completed, transitioning to PROCESSING_BOT'
      );

      try {
        // Convert string input (like DTMF digits) to Transcript object if needed
        // This is necessary because:
        // 1. The state transition validation requires a 'transcript' property of type Transcript
        // 2. We want to handle both speech and DTMF input in a unified way
        // 3. Using a single property simplifies the code and avoids type assertions
        //
        // We use a confidence value of 0.5 for DTMF input because:
        // - It's a reasonable middle ground (DTMF input is accurate but not from speech recognition)
        // - We don't want to assign an artificially high confidence that might skew metrics
        // - We don't want to assign an artificially low confidence that might affect processing
        const transcriptObj: Transcript =
          typeof data.input === 'string'
            ? { text: data.input, confidence: 0.5 } // Convert string to Transcript
            : data.input;

        // Create metadata with unified property for both speech and DTMF input
        const metadata: StateTransitionMetadata = {
          reason: `${data.inputType} input processed`,
          inputType: data.inputType,
          transcript: transcriptObj, // Now always a Transcript object
          requestId: data.requestId,
        };

        // Transition to PROCESSING_BOT state
        await this.setState(SessionState.PROCESSING_BOT, metadata);
      } catch (error) {
        logError(
          `[EventDrivenStateManager] Error transitioning to PROCESSING_BOT: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  }

  /**
   * Handle INTERRUPTION_REQUESTED event
   */
  private async handleInterruptionRequested(data: InterruptionRequestedEventData): Promise<void> {
    const metadata = data.metadata;
    const nextState = metadata.nextState as SessionState;

    if (!nextState) {
      logError('[EventDrivenStateManager] Interruption requested without nextState in metadata');
      return;
    }

    // Interruptions are high-priority and can override most states
    // except for DISCONNECTING and CLOSED
    if (!this.isDisconnectingOrClosed()) {
      try {
        logInfo(`[EventDrivenStateManager] Processing interruption request to state ${nextState}`);
        await this.setState(nextState, {
          ...metadata,
          reason: metadata.reason || 'Interruption requested',
          isInterruption: true,
        });
      } catch (err) {
        logError(
          `[EventDrivenStateManager] Error processing interruption: ${
            err instanceof Error ? err.message : String(err)
          }`
        );
      }
    }
  }

  // --- State management methods ---

  /**
   * Register state actions for a specific state
   */
  public registerStateActions(state: SessionState, actions: StateActions): void {
    this.stateActions[state] = {
      onEnter: actions.onEnter ?? [],
      onExit: actions.onExit ?? [],
    };
  }

  /**
   * Execute all exit actions registered for the given state with the provided context
   */
  private async executeExitActions(state: SessionState, context: StateContext): Promise<void> {
    const actions = this.stateActions[state]?.onExit ?? [];
    logDebug(
      `[EventDrivenStateManager] Executing ${actions.length} exit actions for state ${state}`
    );

    for (let i = 0; i < actions.length; i++) {
      const action = actions[i];
      logDebug(
        `[EventDrivenStateManager] Executing exit action #${i + 1}: ${action.constructor.name}`
      );
      try {
        await action.execute(context);
      } catch (error) {
        logError(
          `[EventDrivenStateManager] Error executing exit action #${i + 1} (${
            action.constructor.name
          }): ${error instanceof Error ? error.message : String(error)}`
        );
      }
    }
  }

  /**
   * Execute all entry actions registered for the given state with the provided context
   */
  private async executeEntryActions(state: SessionState, context: StateContext): Promise<void> {
    const actions = this.stateActions[state]?.onEnter ?? [];
    logDebug(
      `[EventDrivenStateManager] Executing ${actions.length} entry actions for state ${state}`
    );

    for (let i = 0; i < actions.length; i++) {
      const action = actions[i];
      logDebug(
        `[EventDrivenStateManager] Executing entry action #${i + 1}: ${action.constructor.name}`
      );
      try {
        await action.execute(context);
      } catch (error) {
        logError(
          `[EventDrivenStateManager] Error executing entry action #${i + 1} (${
            action.constructor.name
          }): ${error instanceof Error ? error.message : String(error)}`
        );
      }
    }
  }

  /**
   * Transitions the state machine to a new state.
   * This method validates the transition, executes exit actions for the current state,
   * updates the state, and executes entry actions for the new state.
   *
   * @param newState The target state to transition to
   * @param rawMetadata Optional metadata for the transition
   * @returns A promise that resolves when the transition is complete
   * @throws Error if the transition is invalid or fails
   */
  async setState(newState: SessionState, rawMetadata?: Record<string, unknown>): Promise<void> {
    return this.requestTransition(newState, rawMetadata);
  }

  // Handles prioritization, queuing, and preemption for transitions
  private async requestTransition(
    newState: SessionState,
    rawMetadata?: Record<string, unknown>
  ): Promise<void> {
    const oldState = this.getState();

    if (oldState === newState) {
      return;
    }
    if (this.isDisconnectingOrClosed()) {
      logWarning(
        `[EventDrivenStateManager] Ignoring state transition to ${newState} because session is disconnecting or closed.`
      );
      return;
    }

    const metadata = standardizeMetadata(rawMetadata);
    const isPriority = this.isPriorityTransition(newState, metadata);
    const currentTransition = this.lastTransition;
    const isInterruptible = currentTransition
      ? this.isPriorityTransition(currentTransition.to, currentTransition.metadata)
      : false;

    // If a priority transition is requested and the current is interruptible, allow preemption
    if (isPriority && isInterruptible) {
      logDebug(
        `[EventDrivenStateManager] Preempting current transition for priority transition to ${newState}`
      );
      // Optionally, add logic to abort/cleanup the current transition here
    }

    // Serialize all transitions using the mutex
    await this.transitionMutex.run(() => this.doTransition(newState, rawMetadata));
  }

  // Actual transition logic, always called within the mutex
  private async doTransition(
    newState: SessionState,
    rawMetadata?: Record<string, unknown>
  ): Promise<void> {
    const oldState = this.getState();
    const metadata = standardizeMetadata(rawMetadata);

    if (!metadata.previousState) {
      metadata.previousState = oldState;
    }

    if (!this.isValidTransition(oldState, newState, metadata)) {
      const transitionDef = findTransitionDefinition(oldState, newState);
      const description = transitionDef?.description || 'Unknown transition';
      const errorMsg = `Invalid state transition: ${oldState} → ${newState} (${description})`;
      logError(`[EventDrivenStateManager] ${errorMsg}`);
      logError(`[EventDrivenStateManager] Transition reason: ${metadata.reason}`);
      throw new Error(errorMsg);
    }

    this.isTransitionInProgress = true;
    this.lastTransition = {
      from: oldState,
      to: newState,
      metadata,
      timestamp: Date.now(),
      inProgress: true,
      reason: metadata.reason,
    };

    try {
      logDebug(
        `[EventDrivenStateManager] Starting transition: ${oldState} → ${newState} (${metadata.reason})`
      );
      const exitContext: StateContext = {
        state: oldState,
        nextState: newState,
        metadata,
        session: this.session,
      };
      await this.executeExitActions(oldState, exitContext);
      this.state = newState;
      const timestamp = Date.now();
      this.stateHistory.push({
        from: oldState,
        to: newState,
        timestamp,
        reason: metadata.reason,
      });
      this.stateTimestamps[newState].push(timestamp);
      this.logStateChange(oldState, newState, metadata);
      this.notifyListeners(oldState, newState, metadata);
      this.eventEmitter.emit(
        SessionEventType.STATE_CHANGED,
        {
          oldState,
          newState,
          reason: metadata.reason,
          metadata,
          timestamp: Date.now(),
        } as StateChangedEventData,
        EventPriority.HIGH
      );
      const entryContext: StateContext = {
        state: newState,
        metadata,
        session: this.session,
      };
      await this.executeEntryActions(newState, entryContext);
      logDebug(
        `[EventDrivenStateManager] Completed transition: ${oldState} → ${newState} (${metadata.reason})`
      );
    } catch (error) {
      logError(
        `[EventDrivenStateManager] Error during transition ${oldState} → ${newState}: ${error}`
      );
      throw error;
    } finally {
      this.isTransitionInProgress = false;
      if (this.lastTransition) {
        this.lastTransition.inProgress = false;
      }
    }
  }

  /**
   * Determines if a transition is an emergency transition that should be allowed
   * even if another transition is in progress.
   *
   * @param targetState The target state of the transition
   * @returns True if this is an emergency transition, false otherwise
   */
  /**
   * Determines if a transition is a priority transition (e.g., barge-in, disconnect).
   * Barge-in is recognized by targetState PROCESSING_INPUT and metadata.reason === "barge-in".
   * DISCONNECTING and CLOSED are always priority.
   */
  private isPriorityTransition(
    targetState: SessionState,
    metadata?: Record<string, unknown>
  ): boolean {
    if (targetState === SessionState.DISCONNECTING || targetState === SessionState.CLOSED) {
      return true;
    }
    // Recognize barge-in as priority: target PROCESSING_INPUT and reason "barge-in"
    if (
      targetState === SessionState.PROCESSING_INPUT &&
      metadata &&
      typeof metadata.reason === 'string' &&
      metadata.reason.toLowerCase().includes('barge-in')
    ) {
      return true;
    }
    // Add more priority transitions as needed
    return false;
  }

  /**
   * Checks if the current state is DISCONNECTING or CLOSED
   */
  public isDisconnectingOrClosed(): boolean {
    return this.isInState(SessionState.DISCONNECTING) || this.isInState(SessionState.CLOSED);
  }

  /**
   * Generic method to check if the current state matches the specified state
   * This provides a cleaner interface for state checking
   */
  public isInState(state: SessionState): boolean {
    return this.state === state;
  }

  /**
   * Checks if the current state is DISCONNECTING
   */
  public isDisconnecting(): boolean {
    return this.isInState(SessionState.DISCONNECTING);
  }

  /**
   * Checks if the current state is CLOSED
   */
  public isClosed(): boolean {
    return this.isInState(SessionState.CLOSED);
  }

  /**
   * Checks if the current state is PLAYING
   */
  public isPlaying(): boolean {
    return this.isInState(SessionState.PLAYING);
  }

  /**
   * Checks if the current state is PROCESSING_INPUT
   */
  public isProcessingInput(): boolean {
    return this.isInState(SessionState.PROCESSING_INPUT);
  }

  /**
   * Checks if the current state is PROCESSING_BOT
   */
  public isProcessingBot(): boolean {
    return this.isInState(SessionState.PROCESSING_BOT);
  }

  /**
   * Checks if the current state is RESPONDING
   */
  public isResponding(): boolean {
    return this.isInState(SessionState.RESPONDING);
  }

  /**
   * Checks if the session is active (not disconnecting or closed)
   */
  public isActive(): boolean {
    return !this.isDisconnectingOrClosed();
  }

  /**
   * Request interruption for a high-priority event (e.g., barge-in, pause detection).
   * The metadata must include { nextState, ... } for the interruption.
   */
  public requestInterruption(metadata: Record<string, unknown>): void {
    // Emit an event to request interruption
    this.eventEmitter.emit(
      SessionEventType.INTERRUPTION_REQUESTED,
      {
        metadata,
        timestamp: Date.now(),
      },
      EventPriority.HIGH
    );

    logDebug(`[EventDrivenStateManager] Interruption requested: ${JSON.stringify(metadata)}`);
  }

  /**
   * Checks if a state transition is currently in progress.
   */
  public isStateTransitionInProgress(): boolean {
    return this.isTransitionInProgress;
  }

  /**
   * Gets information about the current or most recent transition.
   * @returns An object with from, to, and metadata properties, or null if no transition has occurred.
   */
  public getCurrentTransitionInfo(): {
    from: SessionState;
    to: SessionState;
    metadata?: Record<string, unknown>;
    inProgress: boolean;
  } | null {
    if (!this.lastTransition) {
      return null;
    }

    return {
      from: this.lastTransition.from,
      to: this.lastTransition.to,
      metadata: this.lastTransition.metadata,
      inProgress: this.lastTransition.inProgress,
    };
  }

  /**
   * Validates if a transition from oldState to newState is valid.
   */
  private isValidTransition(
    oldState: SessionState,
    newState: SessionState,
    metadata?: Record<string, unknown>
  ): boolean {
    // Convert metadata to StateTransitionMetadata
    const standardizedMetadata = standardizeMetadata(metadata);

    // Call validateTransition and check the valid flag
    const result = validateTransition(oldState, newState, standardizedMetadata);
    return result.valid;
  }

  /**
   * Logs a state change
   */
  private logStateChange(
    oldState: SessionState,
    newState: SessionState,
    metadata?: Record<string, unknown>
  ): void {
    // Log the state change
    logStateTransition(oldState, newState, 'EventDrivenStateManager', metadata);

    // Log detailed metadata in debug mode
    const loggableMetadata = prepareMetadataForLogging(metadata);
    logDebug(
      `[EventDrivenStateManager] State transition metadata: ${JSON.stringify(loggableMetadata)}`
    );
  }

  // --- State utility methods ---

  /**
   * Gets the current state
   */
  public getState(): SessionState {
    return this.state;
  }

  /**
   * Gets the state history
   */
  public getStateHistory(): StateTransition[] {
    return [...this.stateHistory];
  }

  /**
   * Gets the last time the state was entered
   */
  public getLastStateEntryTime(state: SessionState): number | undefined {
    const timestamps = this.stateTimestamps[state];
    return timestamps.length > 0 ? timestamps[timestamps.length - 1] : undefined;
  }

  /**
   * Gets the duration of the current state
   */
  public getCurrentStateDuration(): number {
    const lastEntry = this.getLastStateEntryTime(this.state);
    return lastEntry ? Date.now() - lastEntry : 0;
  }

  /**
   * Registers a listener for state changes
   */
  public onStateChange(listener: StateChangeListener): () => void {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  /**
   * Notifies listeners of a state change
   */
  private notifyListeners(
    oldState: SessionState,
    newState: SessionState,
    rawMetadata?: Record<string, unknown>
  ): void {
    // Standardize metadata to ensure consistent structure
    const metadata = standardizeMetadata(rawMetadata);

    for (const listener of this.listeners) {
      try {
        listener(oldState, newState, metadata);
      } catch (error) {
        logWarning(`[EventDrivenStateManager] Error in state change listener: ${error}`);
      }
    }
  }
}
