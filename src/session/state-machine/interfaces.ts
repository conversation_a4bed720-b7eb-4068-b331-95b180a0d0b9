/**
 * Core interfaces for the session state machine pattern.
 * Defines contracts for state actions and context.
 *
 * @module session/state-machine/interfaces
 */

import type { ISession } from '../session-interface';
import type { SessionState } from '../session-state-manager';
import type { StateTransitionMetadata } from './types/state-metadata';

/**
 * Provides context for state actions, including the current state,
 * optional metadata, and a reference to the session.
 */
export interface StateContext {
  /** The state for which the action is being executed */
  state: SessionState;
  /** The next state (if this is an exit action during a transition) */
  nextState?: SessionState;
  /** Metadata associated with the transition */
  metadata?: StateTransitionMetadata;
  /** The session instance */
  session: ISession;
}

/**
 * Interface for actions executed on state entry or exit.
 * Actions should emit events to trigger state transitions rather than returning requests.
 */
export interface StateAction {
  /**
   * Execute the action with the provided context.
   * @param context - The state context for this action
   * @returns void
   */
  execute(context: StateContext): Promise<void>;
}

/**
 * Groups entry and exit actions for a state.
 */
export interface StateActions {
  /** Actions to execute when entering the state */
  onEnter?: StateAction[];
  /** Actions to execute when exiting the state */
  onExit?: StateAction[];
}
