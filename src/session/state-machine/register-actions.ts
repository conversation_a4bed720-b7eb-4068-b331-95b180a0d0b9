/**
 * Registers all state actions with the event-driven state manager.
 * This file centralizes the registration of actions for all states.
 *
 * @module session/state-machine/register-actions
 */

import { SessionState } from '../session-state-manager';
import { EventDrivenStateManager } from './event-driven-state-manager';
import { EventEmitter } from '../../services/events/event-emitter';

// Common actions
import { LogStateTransitionAction } from './actions/common/log-state-transition-action';
import { DisposeResourcesAction } from './actions/common/dispose-resources-action';

// Initializing state actions
import { SetConversationIdAction } from './actions/initializing/set-conversation-id-action';
import { InitializeASRServiceAction } from './actions/initializing/initialize-asr-service-action';
import { PrepareForUserInputAction } from './actions/common/prepare-for-user-input-action';

// Audio actions
import { StartPlaybackAction } from './actions/audio/start-playback-action';
import { StopPlaybackAction } from './actions/audio/stop-playback-action';
import { SetPlaybackStateAction } from './actions/audio/set-playback-state-action';
import { HandleBargeInAction } from './actions/audio/handle-barge-in-action';

// Processing Input state actions
import { StartMetricsRequestAction } from './actions/processing-input/start-metrics-request-action';
import { ProcessUserInputAction } from './actions/processing-input/process-user-input-action';
import { TransitionToBotProcessingAction as _TransitionToBotProcessingAction } from './actions/processing-input/transition-to-bot-processing-action';

// Processing Bot state actions
import { ProcessBotStartAction } from './actions/processing-bot/process-bot-start-action';
import { StartLLMMetricsAction } from './actions/processing-bot/start-llm-metrics-action';
import { HandleBotResponseAction } from './actions/processing-bot/handle-bot-response-action';
import { CheckSessionEndAction } from './actions/processing-bot/check-session-end-action';
import { TransitionOutAfterGreetingAction } from './actions/processing-bot/transition-out-after-greeting-action';

/**
 * Register all state actions with the event-driven state manager.
 * @param stateManager The event-driven state manager to register actions with
 * @param eventEmitter The event emitter to use for event-driven actions
 */
export function registerStateActions(
  stateManager: EventDrivenStateManager,
  eventEmitter: EventEmitter
): void {
  // Register INITIALIZING state actions
  stateManager.registerStateActions(SessionState.INITIALIZING, {
    onEnter: [
      new SetConversationIdAction(),
      new InitializeASRServiceAction(),
      new LogStateTransitionAction(),
    ],
    onExit: [],
  });

  // Register IDLE state actions
  stateManager.registerStateActions(SessionState.IDLE, {
    onEnter: [new LogStateTransitionAction(), new PrepareForUserInputAction()],
    onExit: [],
  });

  // Register PROCESSING_INPUT state actions
  // IMPORTANT: ProcessUserInputAction now emits PROCESSING_INPUT_COMPLETED event
  // which is handled by the EventDrivenStateManager to transition to PROCESSING_BOT
  stateManager.registerStateActions(SessionState.PROCESSING_INPUT, {
    onEnter: [
      new LogStateTransitionAction(),
      new StartMetricsRequestAction(),
      new ProcessUserInputAction({ eventEmitter }),
    ],
    onExit: [], // No exit actions needed as transitions are handled by events
  });

  // Register PROCESSING_BOT state actions
  stateManager.registerStateActions(SessionState.PROCESSING_BOT, {
    onEnter: [
      new LogStateTransitionAction(),
      new StartLLMMetricsAction(),
      new ProcessBotStartAction(),
      new TransitionOutAfterGreetingAction({ eventEmitter }),
    ],
    onExit: [
      new HandleBotResponseAction({ eventEmitter }), // This now emits events instead of returning transition requests
      new CheckSessionEndAction(),
      // TransitionToRespondingAction was deprecated and has been removed
    ],
  });

  // Register placeholder actions for other states
  // These will be implemented in future phases

  // Register RESPONDING state actions
  stateManager.registerStateActions(SessionState.RESPONDING, {
    onEnter: [new LogStateTransitionAction(), new StartPlaybackAction()],
    onExit: [new HandleBargeInAction()],
  });

  // Register PLAYING state actions
  stateManager.registerStateActions(SessionState.PLAYING, {
    onEnter: [new LogStateTransitionAction(), new SetPlaybackStateAction('playing')],
    onExit: [new HandleBargeInAction(), new StopPlaybackAction()],
  });

  // Register DISCONNECTING state actions
  stateManager.registerStateActions(SessionState.DISCONNECTING, {
    onEnter: [new LogStateTransitionAction(), new DisposeResourcesAction()],
    onExit: [],
  });

  // Register CLOSED state actions
  stateManager.registerStateActions(SessionState.CLOSED, {
    onEnter: [new LogStateTransitionAction()],
    onExit: [],
  });
}
