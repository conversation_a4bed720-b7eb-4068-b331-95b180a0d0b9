import { SessionState } from '../../session-state-manager';
import { LogStateTransitionAction } from '../actions/common/log-state-transition-action';
import { createMockSession } from './test-utils';
import { EventDrivenStateManager } from '../event-driven-state-manager';
import { EventEmitter } from '../../../services/events/event-emitter';

// Mock the logging module
jest.mock('../../../services/monitoring/logging', () => ({
  logInfo: jest.fn(),
  logDebug: jest.fn(),
  logWarning: jest.fn(),
  logError: jest.fn(),
  logStateTransition: jest.fn(),
  prepareMetadataForLogging: jest.fn().mockImplementation(metadata => metadata),
}));

describe('IDLE state entry actions', () => {
  it('executes LogStateTransitionAction on entry to IDLE', async () => {
    // Create a mock session
    const mockSession = createMockSession();

    // Create an event emitter
    const eventEmitter = new EventEmitter();

    // Create the state manager with the event emitter
    const manager = new EventDrivenStateManager(mockSession, eventEmitter);

    // Create the log action
    const logAction = new LogStateTransitionAction();

    // Register the log action for the IDLE state
    manager.registerStateActions(SessionState.IDLE, {
      onEnter: [logAction],
    });

    // Transition to the IDLE state
    await manager.setState(SessionState.IDLE, { reason: 'Test idle entry' });

    // Verify that the log action was executed
    const { logDebug } = require('../../../services/monitoring/logging');
    expect(logDebug).toHaveBeenCalledWith(
      expect.stringContaining(
        `Session ${mockSession.id} transitioned to state: ${SessionState.IDLE}`
      )
    );
  });
});
