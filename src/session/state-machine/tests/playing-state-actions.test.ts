import { SessionState } from '../../session-state-manager';
import { TransitionToIdleOnPlaybackStoppedAction } from '../actions/audio/transition-to-idle-on-playback-stopped-action';
// Unused imports removed
import { EventEmitter } from '../../../services/events/event-emitter';

describe('PLAYING state actions', () => {
  // Using any for test simplicity
  let session: any;
  let context: any;

  beforeEach(() => {
    // Create a session with mock methods
    session = {
      id: 'test-session-id',
      getSessionState: jest.fn().mockReturnValue(SessionState.PLAYING),
      getStartTime: jest.fn().mockReturnValue(new Date()),
      setPlaybackState: jest.fn(),
      updatePlaybackStateWithoutTransition: jest.fn(),
      enableBargeIn: jest.fn(),
      disableBargeIn: jest.fn(),
      isBargeInEnabled: jest.fn().mockReturnValue(true),
      initializeASRService: jest.fn().mockResolvedValue(undefined),
      processBotStart: jest.fn().mockResolvedValue(undefined),
      getConversationId: jest.fn().mockReturnValue('test-conversation-id'),
      setConversationId: jest.fn(),
      setClientAni: jest.fn(),
      close: jest.fn(),
      send: jest.fn(),
      sendBotTurnResponse: jest.fn(),
      processBinaryMessage: jest.fn().mockResolvedValue(undefined),
      getSessionStateManager: jest.fn().mockReturnValue({
        getState: jest.fn().mockReturnValue(SessionState.PLAYING),
        setState: jest.fn().mockResolvedValue(undefined),
        registerStateActions: jest.fn(),
        getEventEmitter: jest.fn().mockReturnValue(new EventEmitter()),
      }),
      getMetricsAdapter: jest.fn().mockReturnValue({
        startPhase: jest.fn(),
        endPhase: jest.fn(),
        setUserInput: jest.fn(),
        setAiReply: jest.fn(),
        finalizeRequest: jest.fn(),
      }),
      getBotAdapter: jest.fn(),
      getCurrentRequest: jest.fn(),
      getStartMetricsRequest: jest.fn(),
      getLatestBotResponse: jest.fn(),
      setLatestBotResponse: jest.fn(),
      sendAudio: jest.fn().mockResolvedValue(undefined),
      getTTSService: jest.fn(),
      disposeAudio: jest.fn().mockResolvedValue(undefined),
      disposeBot: jest.fn().mockResolvedValue(undefined),
      closeWebSocket: jest.fn(),
      finalizeMetrics: jest.fn(),
      prepareForUserInput: jest.fn().mockResolvedValue(undefined),
      getEventEmitter: jest.fn().mockReturnValue(new EventEmitter()),
    };

    // Create a context with our session
    context = {
      state: SessionState.PLAYING,
      metadata: { reason: 'Test reason' },
      session,
    };
  });

  describe('TransitionToIdleOnPlaybackStoppedAction', () => {
    it('transitions to IDLE if in PLAYING state', async () => {
      // Set up the session state manager
      const stateManager = session.getSessionStateManager();

      // Create the action
      const action = new TransitionToIdleOnPlaybackStoppedAction();

      // Execute the action
      await action.execute(context);

      // Verify that setState was called with the expected parameters
      expect(stateManager.setState).toHaveBeenCalledWith(SessionState.IDLE, {
        reason: 'Audio playback stopped',
      });
    });

    it('clears the latest bot response', async () => {
      // Create the action
      const action = new TransitionToIdleOnPlaybackStoppedAction();

      // Execute the action
      await action.execute(context);

      // Verify that setLatestBotResponse was called with undefined
      expect(session.setLatestBotResponse).toHaveBeenCalledWith(undefined);
    });
  });
});
