import { SessionState } from '../../session-state-manager';
import { StartPlaybackAction } from '../actions/audio/start-playback-action';
import { createMockSession, createMockStateContext } from './test-utils';
import { TestBotResponse, TestTTSService } from './test-types';
import { EventEmitter } from '../../../services/events/event-emitter';

// Mock the logging module
jest.mock('../../../services/monitoring/logging', () => ({
  logInfo: jest.fn(),
  logWarning: jest.fn(),
  logError: jest.fn(),
  logDebug: jest.fn(),
}));

describe('RESPONDING state actions', () => {
  let session: ReturnType<typeof createMockSession>;
  let context: ReturnType<typeof createMockStateContext>;

  beforeEach(() => {
    // Create a session with mock methods
    session = {
      id: 'test-session-id',
      getSessionState: jest.fn().mockReturnValue(SessionState.RESPONDING),
      getStartTime: jest.fn().mockReturnValue(new Date()),
      setPlaybackState: jest.fn(),
      updatePlaybackStateWithoutTransition: jest.fn(),
      enableBargeIn: jest.fn(),
      disableBargeIn: jest.fn(),
      isBargeInEnabled: jest.fn().mockReturnValue(true),
      initializeASRService: jest.fn().mockResolvedValue(undefined),
      processBotStart: jest.fn().mockResolvedValue(undefined),
      getConversationId: jest.fn().mockReturnValue('test-conversation-id'),
      setConversationId: jest.fn(),
      setClientAni: jest.fn(),
      close: jest.fn(),
      send: jest.fn(),
      sendBotTurnResponse: jest.fn(),
      processBinaryMessage: jest.fn().mockResolvedValue(undefined),
      getSessionStateManager: jest.fn().mockReturnValue({
        getState: jest.fn().mockReturnValue(SessionState.RESPONDING),
        setState: jest.fn().mockResolvedValue(undefined),
        registerStateActions: jest.fn(),
        getEventEmitter: jest.fn().mockReturnValue(new EventEmitter()),
        isStateTransitionInProgress: jest.fn().mockReturnValue(false),
      }),
      getMetricsAdapter: jest.fn().mockReturnValue({
        startPhase: jest.fn(),
        endPhase: jest.fn(),
        setUserInput: jest.fn(),
        setAiReply: jest.fn(),
        finalizeRequest: jest.fn(),
      }),
      getBotAdapter: jest.fn(),
      getCurrentRequest: jest.fn(),
      getStartMetricsRequest: jest.fn(),
      getLatestBotResponse: jest.fn(),
      setLatestBotResponse: jest.fn(),
      sendAudio: jest.fn().mockResolvedValue(undefined),
      getTTSService: jest.fn(),
      disposeAudio: jest.fn().mockResolvedValue(undefined),
      disposeBot: jest.fn().mockResolvedValue(undefined),
      closeWebSocket: jest.fn(),
      finalizeMetrics: jest.fn(),
      prepareForUserInput: jest.fn().mockResolvedValue(undefined),
      getEventEmitter: jest.fn().mockReturnValue(new EventEmitter()),
    };

    // Create a context with our session
    context = {
      state: SessionState.RESPONDING,
      metadata: { reason: 'Test reason' },
      session,
    };
  });

  describe('StartPlaybackAction', () => {
    it('sends audio if present in bot response', async () => {
      const action = new StartPlaybackAction();
      const botResponse = new TestBotResponse({ audioBytes: new Uint8Array([1, 2, 3]) });
      session.getLatestBotResponse = () => botResponse;
      await action.execute(context);
      expect(session.sendAudio).toHaveBeenCalledWith(botResponse.audioBytes, undefined);
    });

    it('synthesizes and sends audio if only text is present', async () => {
      const action = new StartPlaybackAction();
      const botResponse = new TestBotResponse({ text: 'hello' });
      session.getLatestBotResponse = () => botResponse;

      // Create a TestTTSService instance
      const ttsService = new TestTTSService(new Uint8Array([4, 5, 6]));

      // Spy on the getAudioBytes method
      jest.spyOn(ttsService, 'getAudioBytes');

      // Set up the session to return the TTS service
      session.getTTSService = () => ttsService;

      // Execute the action
      await action.execute(context);

      // Verify that the TTS service was called to get audio bytes
      expect(ttsService.getAudioBytes).toHaveBeenCalledWith('hello');

      // Verify that the audio was sent
      expect(session.sendAudio).toHaveBeenCalledWith(expect.any(Uint8Array), undefined);
    });

    it('does nothing if no bot response', async () => {
      const action = new StartPlaybackAction();
      session.getLatestBotResponse = () => undefined;
      await action.execute(context);
      expect(session.sendAudio).not.toHaveBeenCalled();
    });

    it('sets playback state to playing', async () => {
      const action = new StartPlaybackAction();
      const botResponse = new TestBotResponse({ audioBytes: new Uint8Array([1]) });
      session.getLatestBotResponse = () => botResponse;
      await action.execute(context);
      expect(session.setPlaybackState).toHaveBeenCalledWith('playing');
    });
  });
});
