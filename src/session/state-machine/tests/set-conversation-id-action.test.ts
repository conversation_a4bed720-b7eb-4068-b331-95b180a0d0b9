import { SetConversationIdAction } from '../actions/initializing/set-conversation-id-action';
import { createMockStateContext, createMockSession } from './test-utils';
import { SessionState } from '../../session-state-manager';
// Unused imports removed

// Mock the logging module
jest.mock('../../../services/monitoring/logging', () => ({
  logInfo: jest.fn(),
  logWarning: jest.fn(),
  logError: jest.fn(),
  logDebug: jest.fn(),
}));

describe('SetConversationIdAction', () => {
  it('sets conversationId on the session', async () => {
    // Create a session with mock methods
    const session = createMockSession();
    session.getConversationId = jest.fn().mockReturnValue(null);
    session.setConversationId = jest.fn();

    // Create a context with our session
    const context = createMockStateContext(
      SessionState.INITIALIZING,
      { conversationId: 'abc123' },
      session
    );

    const action = new SetConversationIdAction();
    await action.execute(context);

    // Verify that setConversationId was called with the expected value
    expect(session.setConversationId).toHaveBeenCalledWith('abc123');
  });

  it('logs a warning if conversationId is missing', async () => {
    // Create a session with mock methods
    const session = createMockSession();
    session.getConversationId = jest.fn().mockReturnValue(null);
    session.setConversationId = jest.fn();

    // Create a context with our session but without conversationId
    const context = createMockStateContext(
      SessionState.INITIALIZING,
      {}, // No conversationId in metadata
      session
    );

    const action = new SetConversationIdAction();

    // This should not throw an error
    await action.execute(context);

    // Verify that setConversationId was not called
    expect(session.setConversationId).not.toHaveBeenCalled();

    // Verify that a warning was logged
    const { logWarning } = require('../../../services/monitoring/logging');
    expect(logWarning).toHaveBeenCalledWith(
      expect.stringContaining('No conversation ID available')
    );
  });

  it('uses existing conversationId if already set on the session', async () => {
    // Create a session with mock methods and an existing conversation ID
    const session = createMockSession();
    session.getConversationId = jest.fn().mockReturnValue('existing-id');
    session.setConversationId = jest.fn();

    // Create a context with our session but without conversationId in metadata
    const context = createMockStateContext(
      SessionState.INITIALIZING,
      {}, // No conversationId in metadata
      session
    );

    const action = new SetConversationIdAction();

    // Execute the action
    await action.execute(context);

    // Verify that setConversationId was not called
    expect(session.setConversationId).not.toHaveBeenCalled();

    // Verify that an info message was logged
    const { logInfo } = require('../../../services/monitoring/logging');
    expect(logInfo).toHaveBeenCalledWith(
      expect.stringContaining('Using existing conversation ID: existing-id')
    );
  });
});
