/**
 * Types for performance monitoring system
 */

/**
 * Phase types for request-level metrics
 */
export type MetricsPhase = 'speechToText' | 'llmProcessing' | 'textToSpeech';

/**
 * Phase timing information
 */
export interface PhaseMetrics {
  startTime: Date;
  endTime: Date | null;
  duration: number | null; // in milliseconds
}

/**
 * Request-level metrics for tracking individual user interactions
 */
export interface RequestMetrics {
  requestId: string;
  timestamp: Date;
  userInput: string;
  aiReply: string;
  phases: {
    speechToText: PhaseMetrics;
    llmProcessing: PhaseMetrics;
    textToSpeech: PhaseMetrics;
  };
  totalDuration: number | null; // Total duration across all phases
}

/**
 * Conversation metrics including request-level metrics
 */
export interface ConversationMetrics {
  conversationId: string;
  ani: string;
  startTime: Date;
  endTime: Date;

  // Request-level metrics
  requests: RequestMetrics[];

  // Phase averages across all requests
  phaseMetrics: {
    speechToText: {
      avgDuration: number;
      minDuration: number;
      maxDuration: number;
      totalDuration: number;
      count: number;
    };
    llmProcessing: {
      avgDuration: number;
      minDuration: number;
      maxDuration: number;
      totalDuration: number;
      count: number;
    };
    textToSpeech: {
      avgDuration: number;
      minDuration: number;
      maxDuration: number;
      totalDuration: number;
      count: number;
    };
  };
}

/**
 * System metrics for system-level monitoring
 */
export interface SystemMetrics {
  timestamp: Date;
  cpu: {
    usage: number;
    loadAverage: number[];
  };
  memory: {
    total: number;
    used: number;
    free: number;
    heapTotal: number;
    heapUsed: number;
    rss: number;
  };
  processMetrics: {
    uptime: number;
    activeHandles: number;
    activeRequests: number;
  };
}

/**
 * Trend metrics for performance trends
 */
export interface TrendMetrics {
  timeRange: string;
  contentCategory: {
    xs: number;
    s: number;
    m: number;
    l: number;
  };
  averageProcessingTimes: {
    xs: number;
    s: number;
    m: number;
    l: number;
  };
  contextSizeImpact: {
    smallContext: number;
    mediumContext: number;
    largeContext: number;
  };
}
