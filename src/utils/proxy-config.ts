/**
 * Returns proxy configuration for HTTP requests.
 * Automatically bypasses proxy for localhost and internal requests.
 *
 * @param url The URL for the request
 * @returns Proxy configuration object or undefined to bypass proxy
 */
export function getProxyConfig(url: string) {
  // Bypass proxy for localhost and internal requests
  if (url.includes('localhost') || url.includes('127.0.0.1') || url.includes('::1')) {
    console.log(`[PROXY] Bypassing proxy for local request: ${url}`);
    return undefined;
  }

  // Respect NO_PROXY environment variable
  const noProxy = process.env.NO_PROXY || process.env.no_proxy;
  if (noProxy) {
    const noProxyList = noProxy
      .split(',')
      .map(entry => entry.trim())
      .filter(Boolean);
    try {
      const { hostname } = new URL(url);
      if (
        noProxyList.some(
          noProxyHost =>
            noProxyHost === '*' ||
            hostname === noProxyHost ||
            (noProxyHost.startsWith('.') && hostname.endsWith(noProxyHost)) ||
            (noProxyHost.length > 0 && hostname.endsWith(noProxyHost))
        )
      ) {
        console.log(`[PROXY] Bypassing proxy for NO_PROXY match: ${url}`);
        return undefined;
      }
    } catch (e) {
      // If URL parsing fails, fallback to not bypassing
    }
  }

  // Use proxy from environment variables for external requests
  const proxyUrl = process.env.HTTPS_PROXY || process.env.HTTP_PROXY;

  if (!proxyUrl) {
    console.log(`[PROXY] No proxy configured for external request: ${url}`);
    return undefined;
  }

  // Check if the proxy URL is empty string
  if (proxyUrl.trim() === '') {
    console.log(`[PROXY] Empty proxy string found, bypassing proxy for: ${url}`);
    return undefined;
  }

  console.log(`[PROXY] Using proxy for external request: ${url} -> ${proxyUrl}`);

  try {
    const parsedUrl = new URL(proxyUrl);
    return {
      host: parsedUrl.hostname,
      port: parseInt(parsedUrl.port),
      protocol: parsedUrl.protocol.replace(':', ''),
    };
  } catch (error) {
    console.error(`Invalid proxy URL: ${proxyUrl}`, error);
    return undefined;
  }
}
