/**
 * Hand<PERSON> for playback_started messages from the client.
 * Updates the session state to PLAYING if appropriate.
 */
import { ClientMessage } from '../../../protocol/message';
import { Session } from '../../../session/session';
import { MessageHandler } from '../message-handler';
import { getASRService } from '../../../services/asr-service';
import { logInfo, logWarning, logDebug } from '../../../services/monitoring/logging';
import { SessionState } from '../../../session/session-state-manager';

export class PlaybackStartedMessageHandler implements MessageHandler {
  async handleMessage(_message: ClientMessage, session: Session) {
    // Get current state before attempting to change it
    const currentState = session.getSessionState();
    const stateManager = session.getSessionStateManager();

    // Check if barge-in is enabled for this session
    const bargeInEnabled = session.isBargeInEnabled();

    // Only transition to PLAYING if we're in a valid state for playback
    // Valid states for transitioning to PLAYING are RESPONDING or IDLE
    if (currentState === SessionState.RESPONDING) {
      // Transition to PLAYING state
      await stateManager.setState(SessionState.PLAYING, {
        reason: 'Client reported playback started',
        playbackStatus: 'playing',
        clientConfirmed: true,
        bargeInEnabled: bargeInEnabled,
      });

      logInfo('[PlaybackStarted] Transitioned from RESPONDING to PLAYING state');
    } else if (currentState === SessionState.PLAYING) {
      // Already in PLAYING state, just log it
      logInfo('[PlaybackStarted] Already in PLAYING state, no transition needed');
    } else {
      // If we're in an unexpected state, log a warning but don't attempt an invalid transition
      logWarning(
        `[PlaybackStarted] Received playback_started message while in ${currentState} state. Cannot transition to PLAYING.`
      );
    }

    // Control the ASR service based on barge-in setting
    try {
      const asrService = await getASRService();

      if (bargeInEnabled) {
        logInfo('[Event] Playback Started - Continuing audio input processing for barge-in');
        // Ensure ASR service continues processing audio for barge-in
        asrService.stopIgnoringAudioInput();
        logDebug('[BARGE-IN] ASR service isIgnoringAudioInput flag explicitly set to FALSE');
      } else {
        logInfo('[Event] Playback Started - Pausing audio input processing');
        // Ignore audio input during playback when barge-in is disabled
        asrService.startIgnoringAudioInput();
      }
    } catch (error) {
      console.error('[PlaybackStarted] Error controlling ASR service:', error);
    }
  }
}
